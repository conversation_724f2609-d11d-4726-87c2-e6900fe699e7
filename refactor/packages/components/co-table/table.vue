<!--
  Co-Table 主表格组件

  这是一个功能完整的数据表格组件，提供以下核心功能：
  - 数据展示与分页
  - 内置搜索功能
  - 表格内编辑
  - 操作按钮与权限控制
  - 字典数据支持
  - 文件上传下载
  - 行选择与批量操作
  - 自定义渲染与插槽

  使用场景：
  - 后台管理系统的数据表格
  - 需要复杂交互的数据展示
  - 带搜索和操作的数据管理界面
-->
<template>
  <!--
    表格页面容器
    - class="zs-table-page": 应用表格页面样式
    - :style: 动态设置CSS变量，用于主题色和样式定制
  -->
  <div
    class="zs-table-page"
    :style="{
      '--highlight-color': highlightColor,                                    // 高亮颜色CSS变量
      '--selection-text': `'${selectionText}'`,                              // 选择文本CSS变量
      '--header-color': mergeAttrs['header-cell-style']?.color,              // 表头颜色CSS变量
    }"
  >
    <!-- 搜索表单区域 -->
    <template v-if="search">
      <!--
        搜索组件
        - ref="searchRef": 搜索组件引用
        - scene="inTable": 标识在表格内使用
        - :dic: 传递字典数据（只有在字典加载完成后才传递）
        - v-model:model: 双向绑定搜索表单数据
        - :config: 搜索表单配置
        - @search: 监听搜索事件
        - @change: 监听搜索条件变化事件
      -->
      <co-search
        ref="searchRef"
        scene="inTable"
        :dic="dicLoaded ? dicEnumData : null"
        v-model:model="searchFormData"
        :config="search"
        @search="onSearch"
        @change="(prop, value) => emit('search-change', prop, value)"
      >
        <!-- 搜索操作按钮插槽 -->
        <template #search_operation="{ handle }">
          <slot name="search_operation" :handle="handle" />
        </template>
      </co-search>
    </template>
    <!--
      表格容器
      - v-loading="vloading": 显示加载状态
      - class="zs-table-container": 应用表格容器样式
    -->
    <div v-loading="vloading" class="zs-table-container">
      <!-- 顶部操作按钮区域 -->
      <div v-if="!!topOperationList.length" class="top-operation">
        <!--
          顶部操作按钮插槽
          - name="topOperation": 插槽名称
          - :list="topOperationList": 传递操作按钮列表
        -->
        <slot name="topOperation" :list="topOperationList">
          <!--
            默认操作按钮渲染
            - v-for: 遍历操作按钮列表
            - :key="item.mark": 使用标识作为唯一键
            - :item: 传递按钮配置
            - @click: 处理按钮点击事件
          -->
          <co-button
            v-for="item in topOperationList"
            :key="item.mark"
            :item="item"
            @click="dispatchHandle({ field: item.mark, btn: item, id: _tableKey })"
          />
        </slot>
        <!-- 顶部操作文本插槽 -->
        <slot name="topOperationText"></slot>
      </div>

      <!--
        表格容器组件
        - ref="tableForm": 表格表单引用
        - :model: 传递表格数据模型
        - :has-form-item: 是否包含表单项
        - :config-opts: 配置选项
      -->
      <co-container
        ref="tableForm"
        :model="{ data: tableData }"
        :has-form-item="hasFormItem"
        :config-opts="{ size: $attrs.size || 'default' }"
      >
        <!--
          Element Plus 表格组件
          - :ref: 动态表格引用名称
          - :data: 表格数据源
          - :header-cell-class-name: 表头单元格样式类名
          - :row-key: 行数据的唯一标识字段
          - :row-class-name: 行样式类名函数
          - v-bind="mergeAttrs": 绑定合并后的表格属性
          - class="zs-table": 应用表格样式
        -->
        <el-table
          :ref="_tableKey + '_elTableRef'"
          :data="tableData"
          :header-cell-class-name="cellClass"
          :row-key="$attrs['row-key'] || '_uuid'"
          :row-class-name="_rowClassName"
          v-bind="mergeAttrs"
          class="zs-table"
        >
          <!-- 展开行列 -->
          <el-table-column v-if="$slots['expand']" type="expand">
            <template #default="scope">
              <slot name="expand" v-bind="scope" />
            </template>
          </el-table-column>

          <!-- 选择列 -->
          <el-table-column
            v-if="_selection"
            type="selection"
            :width="_selection.width || 55"
            :align="_selection.align || align"
            :selectable="_selection.selectable"
            :reserve-selection="$attrs['reserve-selection'] || false"
          />

          <!-- 序号列 -->
          <el-table-column
            v-if="$attrs.showIndex"
            type="index"
            :label="$attrs.indexLabel || '序号'"
            :width="$attrs.indexWidth || 60"
            :align="align"
          />

          <!-- 动态表格列 -->
          <template v-for="(item, index) in tbHeader" :key="item.prop || index">
            <el-table-column
              v-bind="item"
              :align="item.align || align"
              :show-overflow-tooltip="item.showOverflowTooltip !== false"
            >
              <!-- 表头插槽 -->
              <template v-if="$slots[`${item.prop}_header`]" #header="scope">
                <slot :name="`${item.prop}_header`" v-bind="scope" />
              </template>

              <!-- 单元格内容 -->
              <template #default="scope">
                <!-- 自定义插槽 -->
                <slot
                  v-if="$slots[item.prop]"
                  :name="item.prop"
                  v-bind="scope"
                  :item="item"
                />
                <!-- 表单组件 -->
                <component
                  v-else-if="item.type && ['input', 'select', 'switch', 'textarea', 'inputNumber', ...dateType].includes(item.type)"
                  :is="getFormComponentName(item.type)"
                  v-bind="getFormComponentProps(item, scope)"
                  @change="onFormItemChange"
                />
                <!-- 静态内容 -->
                <static-com
                  v-else
                  :item="item"
                  :row="scope.row"
                  :value="scope.row[item.prop]"
                />
              </template>
            </el-table-column>
          </template>

          <!-- 操作列 -->
          <el-table-column
            v-if="!_hiddenOperation && operationList.length"
            :label="operationOpts.label || '操作'"
            :width="operationOpts.width"
            :align="operationOpts.align || align"
            :fixed="operationOpts.fixed"
          >
            <template #default="scope">
              <!-- 操作按钮插槽 -->
              <slot name="operation" v-bind="scope" :list="getRowOperationList(scope.row)">
                <!-- 默认操作按钮 -->
                <template v-for="btn in getRowOperationList(scope.row)" :key="btn.mark">
                  <co-button
                    v-if="!btn.hidden"
                    :item="btn"
                    @click="dispatchHandle({ field: btn.mark, btn, row: scope.row, index: scope.$index, id: _tableKey })"
                  />
                </template>
              </slot>
            </template>
          </el-table-column>
        </el-table>
      </co-container>

      <!-- 分页组件 -->
      <div v-if="showPagination && _paging" class="pagination-container">
        <el-pagination
          v-bind="_paging"
          :current-page="paging.current"
          :page-size="paging['page-size']"
          :total="paging.total"
          @size-change="onSizeChange"
          @current-change="onCurrentChange"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
/**
 * Co-Table 主表格组件
 *
 * 这是一个功能完整的数据表格组件，集成了以下核心功能：
 * - 数据展示与分页
 * - 内置搜索功能
 * - 表格内编辑
 * - 操作按钮与权限控制
 * - 字典数据支持
 * - 文件上传下载
 * - 行选择与批量操作
 * - 自定义渲染与插槽
 *
 * 主要特性：
 * - 支持单选和多选模式
 * - 支持表格内表单验证
 * - 支持权限控制的按钮显示
 * - 支持复杂的数据处理逻辑
 * - 支持自定义样式和主题
 *
 * <AUTHOR> 4.0 sonnet
 * @version 1.0.0
 */

// 导入Vue 3 Composition API
import { ref, reactive, computed, watch, provide, useAttrs, useSlots } from 'vue';
// 导入工具类和配置
import Utils from "./utils/index.js";
import defaultConfig, { dateType } from "./config.js";
// 导入子组件
import coUpload from "./components/upload/index.vue";
import coButton from "./components/co-button/index.vue";
import coContainer from "./components/co-container/index.vue";
import staticCom from "./components/static-component.vue";
import coFormItem from "./components/co-form-item/index.js";
import coSearch from "./search.vue";
// 导入表格逻辑工具
import tableJs from "./table.js";

// 组件名称定义
defineOptions({
  name: "CoTable",
  inheritAttrs: false, // 不继承父组件的属性
  components: {
    coUpload,        // 上传组件
    ...coFormItem,   // 表单组件集合
    coSearch,        // 搜索组件
    coButton,        // 按钮组件
    coContainer,     // 容器组件
    staticCom,       // 静态组件
  },
});

// 获取父组件传递的属性和插槽
const attrs = useAttrs();
const slots = useSlots();
</script>
