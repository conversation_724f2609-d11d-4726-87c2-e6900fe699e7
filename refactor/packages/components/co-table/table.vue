<!--
  Co-Table 主表格组件

  这是一个功能完整的数据表格组件，提供以下核心功能：
  - 数据展示与分页
  - 内置搜索功能
  - 表格内编辑
  - 操作按钮与权限控制
  - 字典数据支持
  - 文件上传下载
  - 行选择与批量操作
  - 自定义渲染与插槽

  使用场景：
  - 后台管理系统的数据表格
  - 需要复杂交互的数据展示
  - 带搜索和操作的数据管理界面
-->
<template>
  <!--
    表格页面容器
    - class="zs-table-page": 应用表格页面样式
    - :style: 动态设置CSS变量，用于主题色和样式定制
  -->
  <div
    class="zs-table-page"
    :style="{
      '--highlight-color': highlightColor,                                    // 高亮颜色CSS变量
      '--selection-text': `'${selectionText}'`,                              // 选择文本CSS变量
      '--header-color': mergeAttrs['header-cell-style']?.color,              // 表头颜色CSS变量
    }"
  >
    <!-- 搜索表单区域 -->
    <template v-if="search">
      <!--
        搜索组件
        - ref="searchRef": 搜索组件引用
        - scene="inTable": 标识在表格内使用
        - :dic: 传递字典数据（只有在字典加载完成后才传递）
        - v-model:model: 双向绑定搜索表单数据
        - :config: 搜索表单配置
        - @search: 监听搜索事件
        - @change: 监听搜索条件变化事件
      -->
      <co-search
        ref="searchRef"
        scene="inTable"
        :dic="dicLoaded ? dicEnumData : null"
        v-model:model="searchFormData"
        :config="search"
        @search="onSearch"
        @change="(prop, value) => $emit('search-change', prop, value)"
      >
        <!-- 搜索操作按钮插槽 -->
        <template #search_operation="{ handle }">
          <slot name="search_operation" :handle="handle" />
        </template>
      </co-search>
    </template>
    <!--
      表格容器
      - v-loading="vloading": 显示加载状态
      - class="zs-table-container": 应用表格容器样式
    -->
    <div v-loading="vloading" class="zs-table-container">
      <!-- 顶部操作按钮区域 -->
      <div v-if="!!topOperationList.length" class="top-operation">
        <!--
          顶部操作按钮插槽
          - name="topOperation": 插槽名称
          - :list="topOperationList": 传递操作按钮列表
        -->
        <slot name="topOperation" :list="topOperationList">
          <!--
            默认操作按钮渲染
            - v-for: 遍历操作按钮列表
            - :key="item.mark": 使用标识作为唯一键
            - :item: 传递按钮配置
            - @click: 处理按钮点击事件
          -->
          <co-button
            v-for="item in topOperationList"
            :key="item.mark"
            :item="item"
            @click="dispatchHandle({ field: item.mark, btn: item, id: _tableKey })"
          />
        </slot>
        <!-- 顶部操作文本插槽 -->
        <slot name="topOperationText"></slot>
      </div>

      <!--
        表格容器组件
        - ref="tableForm": 表格表单引用
        - :model: 传递表格数据模型
        - :has-form-item: 是否包含表单项
        - :config-opts: 配置选项
      -->
      <co-container
        ref="tableForm"
        :model="{ data: tableData }"
        :has-form-item="hasFormItem"
        :config-opts="{ size: $attrs.size || 'default' }"
      >
        <!--
          Element Plus 表格组件
          - :ref: 动态表格引用名称
          - :data: 表格数据源
          - :header-cell-class-name: 表头单元格样式类名
          - :row-key: 行数据的唯一标识字段
          - :row-class-name: 行样式类名函数
          - v-bind="mergeAttrs": 绑定合并后的表格属性
          - class="zs-table": 应用表格样式
        -->
        <el-table
          :ref="_tableKey + '_elTableRef'"
          :data="tableData"
          :header-cell-class-name="cellClass"
          :row-key="$attrs['row-key'] || '_uuid'"
          :row-class-name="_rowClassName"
          v-bind="mergeAttrs"
          class="zs-table"
        >
          <!-- 展开行列 -->
          <el-table-column v-if="$slots['expand']" type="expand">
            <template #default="{ row, column, $index }">
              <!-- 展开行内容插槽 -->
              <slot name="expand" v-bind="{ row, column, $index }" />
            </template>
          </el-table-column>

          <!-- 空数据状态插槽 -->
          <template v-slot:empty v-if="$slots['empty']">
            <slot :name="'empty'">暂无数据</slot>
          </template>

          <!-- 选择列：当配置了选择功能时显示 -->
          <template v-if="_selection">
            <el-table-column
              type="selection"
              v-bind="_selection"
              :align="_selection.align || align"
            />
          </template>
          <!-- 动态渲染表格列 -->
          <template v-for="item in tbHeader">
            <!--
              表格列组件
              - v-if="!item.hidden": 只渲染非隐藏的列
              - :key="item.prop": 使用属性名作为唯一标识
              - v-bind="item": 绑定列的所有配置属性
              - :align: 列对齐方式
            -->
            <el-table-column
              v-if="!item.hidden"
              :key="item.prop"
              v-bind="item"
              :align="item.align || align"
            >
              <!-- 自定义表头插槽 -->
              <template v-if="$slots[item.prop + '_header']" #header="slotHeader">
                <slot :name="item.prop + '_header'" v-bind="slotHeader" />
              </template>

              <!-- 列内容渲染（非索引列） -->
              <template v-if="item.type !== 'index'" #default="{ row, column }">
                <!-- 表单组件类型的列（可编辑） -->
                <template
                  v-if="['input', 'select', 'switch', 'inputNumber'].includes(item.type) || isDateType(item.type)"
                >
                  <!-- 自定义表单项插槽 -->
                  <slot
                    v-if="$slots[item.prop + '_form-item']"
                    :dicEnum="dicEnum[item.prop]"
                    v-bind="{
                      row,
                      column,
                      $index: row._index,
                      item,
                      prop: item.prop,
                    }"
                    :name="item.prop + '_form-item'"
                  />

                  <!--
                    默认表单项渲染
                    - :key="row._selected": 根据选中状态重新渲染
                    - :prop: 表单验证路径
                    - :rules: 验证规则（只有在选中或验证模式下才应用）
                    - :class: 开关组件居中对齐样式
                  -->
                  <el-form-item
                    v-else
                    :key="row._selected"
                    :prop="row._propPath + '.' + item.prop"
                    :rules="row._selected || isValidate ? item?.rules : []"
                    :class="{ 'table-switch-align': align === 'center' }"
                  >
                    <!-- 表单组件内容插槽 -->
                    <slot
                      :name="item.prop"
                      :dicEnum="dicEnum[item.prop]"
                      v-bind="{
                        row,
                        column,
                        $index: row._index,
                        item,
                        prop: item.prop,
                      }"
                    >
                      <!--
                        动态表单组件
                        - :is: 根据类型动态选择组件
                        - :type: 组件类型
                        - scene="inTable": 标识在表格内使用
                        - :form-ref: 表单引用
                        - :index: 行索引
                        - :item: 列配置
                        - :dic: 字典数据
                        - :row: 行数据
                        - :handle: 事件处理函数
                      -->
                      <component
                        :is="'co-' + (isDateType(item.type) ? 'date' : item.type)"
                        :type="item.type"
                        scene="inTable"
                        :form-ref="_tableFormRef"
                        :index="row._index"
                        :item="item"
                        :dic="dicEnumData"
                        :row="row"
                        :handle="dispatchHandle"
                      />
                    </slot>
                  </el-form-item>
                </template>
                <template v-else>
                  <slot
                    :name="item.prop"
                    :dicEnum="dicEnum[item.prop]"
                    v-bind="{
                      row,
                      column,
                      $index: row._index,
                      item,
                      prop: item.prop,
                    }"
                  >
                    <template v-if="dicKeyArr.includes(item.prop) && dicLoaded">
                      <span hidden>{{
                        ([propStyle, propData] = [
                          dicEnum[item.prop]["color"] && dicEnum[item.prop]["color"][row[item.prop]],
                          dicEnum[item.prop]["data"][row[item.prop]],
                        ])
                      }}</span>
                      <template v-if="propStyle">
                        <span v-if="propStyle.includes('#')" :style="{ color: propStyle }">{{ propData || "-" }}</span>
                        <span v-else :class="propStyle">{{ propData || "-" }}</span>
                      </template>
                      <span v-else>{{ propData || "-" }}</span>
                    </template>
                    <template v-else-if="item.type === 'upload'">
                      <co-upload v-bind="item" @onSuccess="uploadSuccess($event, row, item, row._index)" />
                    </template>
                    <static-com
                      v-else
                      :item="item"
                      :column="column"
                      :index="row._index"
                      :row="row"
                      :handle="item.type === 'download' ? onDownLoad : dispatchHandle"
                    />
                  </slot>
                </template>
              </template>
            </el-table-column>
          </template>
          <template v-if="!_hiddenOperation">
            <el-table-column
              v-if="operationList.length"
              :fixed="operationOpts.fixed"
              :align="operationOpts.align || align"
              :label="operationOpts.label || '操作'"
              :width="operationOpts.width"
            >
              <template v-if="$slots['operation_header']" #header="slotHeader">
                <slot :name="'operation_header'" v-bind="slotHeader" />
              </template>
              <template #default="{ row }">
                <slot
                  :name="'operation'"
                  :list="inTableRowBtn[currentRowKey + row[currentRowKey]]"
                  :row="row"
                  :index="row._index"
                >
                  <template v-if="Object.keys(showPermisBtn).length > 0">
                    <co-button
                      v-for="item in showPermisBtn[currentRowKey + row[currentRowKey]]"
                      :key="item.mark"
                      :item="item"
                      @click="
                        dispatchHandle({
                          field: item.mark,
                          btn: item,
                          row,
                          index: row._index,
                          id: _tableKey,
                        })
                      "
                    />
                    <el-dropdown
                      v-if="
                        showPermisBtn[currentRowKey + row[currentRowKey]].length !==
                        inTableRowBtn[currentRowKey + row[currentRowKey]].length
                      "
                      :trigger="operationOpts.more.trigger"
                      :hide-on-click="false"
                      style="padding-left: 10px"
                      @command="dispatchHandle"
                    >
                      <co-button :item="operationOpts.more.attrs" dis-click>
                        {{ operationOpts.more.text }}<i class="el-icon-arrow-down el-icon--right" />
                      </co-button>
                      <template v-slot:dropdown>
                        <el-dropdown-menu :style="{ width: operationOpts.more.width }">
                          <el-dropdown-item
                            v-for="item in inTableRowBtn[currentRowKey + row[currentRowKey]].slice(
                              operationOpts.showCount
                            )"
                            :key="item.mark"
                            :command="{
                              field: item.mark,
                              btn: item,
                              row,
                              index: row._index,
                            }"
                          >
                            <co-button :item="item" :dis-click="true" />
                          </el-dropdown-item>
                        </el-dropdown-menu>
                      </template>
                    </el-dropdown>
                  </template>
                </slot>
              </template>
            </el-table-column>
          </template>
          <template #append>
            <slot name="append" />
          </template>
        </el-table>
      </co-container>
      <div v-if="showPagination" class="zs-table-pagination" :style="{ '--page-align': _paging.align }">
        <el-pagination
          v-bind="_paging"
          :page-size="_paging['page-size']"
          :current-page="_paging.current"
          :total="+_paging.total"
          @size-change="handlePaging($event, 'page-size')"
          @current-change="handlePaging($event, 'current')"
        />
      </div>
    </div>
  </div>
</template>

<script>
import Utils from "./utils/index.js";
import defaultConfig, { dateType } from "./config.js";
import coUpload from "./components/upload/index.vue";
import coButton from "./components/co-button/index.vue";
import coContainer from "./components/co-container/index.vue";
import staticCom from "./components/static-component.vue";
import coFormItem from "./components/co-form-item/index.js";
import coSearch from "./search.vue";
import tableJs from "./table.js";

import { reactive } from "vue";

/**
 * Co-Table 主表格组件
 *
 * 这是一个功能完整的数据表格组件，集成了以下核心功能：
 * - 数据展示与分页
 * - 内置搜索功能
 * - 表格内编辑
 * - 操作按钮与权限控制
 * - 字典数据支持
 * - 文件上传下载
 * - 行选择与批量操作
 * - 自定义渲染与插槽
 *
 * 主要特性：
 * - 支持单选和多选模式
 * - 支持表格内表单验证
 * - 支持权限控制的按钮显示
 * - 支持复杂的数据处理逻辑
 * - 支持自定义样式和主题
 *
 * <AUTHOR> 4.0 sonnet
 * @version 1.0.0
 */
export default {
  name: "CoTable",

  // 注册所有子组件
  components: {
    coUpload,        // 上传组件
    ...coFormItem,   // 表单组件集合
    coSearch,        // 搜索组件
    coButton,        // 按钮组件
    coContainer,     // 容器组件
    staticCom,       // 静态组件
  },

  // 不继承父组件的属性
  inheritAttrs: false,

  /**
   * 组件属性定义
   */
  props: {
    // 表格数据源
    data: {
      type: Array,
      default: () => [],
    },

    // 单选模式配置
    singleMode: {
      type: [Boolean, String],
      default: "",
    },

    // 是否启用点击行选择功能
    currentRow: {
      type: Boolean,
      default: false,
    },

    // 选中行的高亮背景色
    highlightColor: {
      type: String,
      default: "",
    },

    // 表格数据的对齐方式
    align: {
      type: String,
      default: "center",
      validator: (value) => ["left", "center"].includes(value),
    },

    // 表格列配置数组
    header: {
      type: Array,
      default: () => [],
    },
    // 搜索配置 配置后显示搜索组件
    search: {
      type: [Boolean, Object],
      default: false,
    },
    // 接口额外参数
    params: {
      type: Object,
      default: () => ({}),
    },
    // 定义是否启用 v-loading 会覆盖全局配置的loading
    loading: {
      type: Boolean,
      default: false,
    },
    // 表格主要配置
    config: {
      type: Object,
      default: () => ({}),
    },
    // 是否验证所有表单
    isValidate: {
      type: Boolean,
      default: false,
    },
  },
  // 没用，但不可删除，为了解决搜索组件重置
  provide() {
    return {
      widgetItem: {},
      formModel: {},
    };
  },
  data() {
    return {
      selectionText: "",
      searchFormData: this.params || {}, // 内置搜索组件 回显参数
      dataList: [],
      tbHeader: [],
      currentRowKey: "",
      vloading: false,
      operationList: [],
      paging: {
        align: "right",
        "page-size": 10,
        current: 1,
        total: 0,
        background: true,
        "page-sizes": [10, 20, 30, 50, 100],
        layout: "total, sizes, prev, pager, next, jumper",
      },
      inTableRowBtn: {}, // table内所有匹配权限的按钮
      showPermisBtn: {}, // table内展示的按钮 从inTableRowBtn中过滤（非折叠按钮)
      selectedData: [],
      singleData: "", // 单选模式下选中的数据
      dicEnum: {},
      dicEnumData: {},
      loadDicNum: 0,
      showPagination: true, // 默认true：展示分页组件
      dicKeyArr: [],
    };
  },
  computed: {
    hasFormItem() {
      const { tbHeader } = this;
      return !!tbHeader.some((item) =>
        ["input", "select", "switch", "textarea", "inputNumber", ...dateType].includes(item.type)
      );
    },
    // 判断 字典是否加载完成
    dicLoaded() {
      const { loadDicNum, dicKeyArr } = this;
      return loadDicNum === dicKeyArr.length;
    },
    // 过滤出table顶部按钮
    topOperationList() {
      const { operationList, _tableKey } = this;
      return operationList.length
        ? operationList.filter((btn) => +btn.inTable === 2 && (btn.tableKey === _tableKey || !btn.tableKey))
        : [];
    },
    // 分页数据变化
    _paging() {
      const { config, paging } = this;
      const { pagination } = config;
      if (Utils.getType(pagination) === "Object") {
        Object.assign(paging, pagination);
        if (!pagination["page-sizes"] && !paging["page-sizes"].includes(paging["page-size"]))
          paging["page-sizes"].push(paging["page-size"]);
        return paging;
      }
      return typeof pagination === "boolean" ? pagination : paging;
    },
    tableData({ data, dataList }) {
      let resData = dataList;
      if (data.length) {
        resData = data;
        this.renderOperation();
        if (!resData[0]._uuid) {
          this.setPrivateKey({ data: resData });
        }
        this.emitData(resData);
      }
      return resData;
    },
  },
  watch: {
    header: {
      immediate: true,
      handler(n) {
        // 获取selection列
        this._selection = (this.config?.header || n).find((item) => item.type === "selection");
        this._selection && (this.selectionText = this._selection.label);
        // 表头
        this.tbHeader = this._selection ? n.filter((item) => !["selection"].includes(item.type)) : n;
      },
    },
  },
  created() {
    this._tableFormRef = null;
    const { config, $attrs } = this;
    this.mergeAttrs = { ...defaultConfig.attrs, ...$attrs };
    this.currentRowKey =
      typeof $attrs["row-key"] === "string" ? $attrs["row-key"] : $attrs["current-row-key"] || "_uuid";
    this.attrsKeys = Object.keys($attrs);
    // 唯一的tableKey
    this._tableKey = $attrs.id || "";
    this.returnSetFn = {
      setHeader: this.setHeader,
      setRow: this.setRow,
      setData: this.setData,
      setPage: this.setPage,
      setParams: this.setParams,
    };
    this.searchParamsCache = {};
    // 初始 合并分页数据
    const pagination = config.pagination;
    if (typeof pagination === "boolean") {
      this.showPagination = pagination;
    } else {
      Object.assign(this.paging, pagination);
    }
    // 获取树型结构的tree-props
    this._childrenKey = ($attrs["tree-props"] && $attrs["tree-props"].children) || "children";
    // vue3 不兼容，暂时去掉
    // this._listeners = tableJs.initListeners.call(this, this.$attrs);
    // 操作列配置 将config.operation与默认配置合并
    this._hiddenOperation = config.operation === false;
    const defaultOpts = {
      showCount: -1,
      fixed: false,
      trigger: "hover",
      more: {
        width: 200,
        text: "更多",
        attrs: { type: "text", size: $attrs.size },
      },
    };
    this.operationOpts = config.operation ? Utils.deepMerge(defaultOpts, config.operation) : defaultOpts;
  },
  mounted() {
    const { config } = this;
    // 是否有request .apiName属性
    this._hasRequestApi = config.request && config.request.apiName;
    // el-table 自身ref实例
    this.elTableRef = this.$refs[this._tableKey + "_elTableRef"];
    // 执行loaded回调 {elTable:el-table的ref，getDataList:获取数据的接口方法}
    this.$emit("loaded", {
      elTableRef: this.elTableRef,
      getDataList: this.requestData,
      ...this.returnSetFn,
    });
    // 获取 table内form ref
    this._tableFormRef = this.$refs.tableForm.formRef();
    // 初始化表格所有字典 dic:{Object}
    const dicConfig = config.dic;
    // dic转为key array
    this.dicKeyArr = dicConfig ? Object.keys(dicConfig) : [];
    if (this.dicKeyArr.length) {
      this.$nextTick(() => {
        for (const [dicKey, dicBody] of Object.entries(dicConfig)) {
          // 如果没有data属性 执行自定义data 数据（Array to Object）  否则 请求远程字典
          if (
            typeof dicBody === "function" ||
            typeof dicBody === "string" ||
            (dicBody.value && Utils.getType(dicBody.value) === "String") ||
            (dicBody.data && Utils.getType(dicBody.data) === "String")
          ) {
            this.getRemoteDic(dicKey, dicBody);
          } else {
            this.transferDic(dicKey, dicBody);
          }
        }
      });
    } else {
      // 获取表格数据
      this.init();
    }
  },
  methods: {
    init() {
      if (this._hasRequestApi) {
        this.renderOperation();
        this.requestData({ model: "" });
      }
    },
    emitData(dataList) {
      this.$emit("data", dataList);
    },

    setPrivateKey({ data }) {
      const { _hiddenOperation, _childrenKey, hasFormItem } = this;
      // 设置row中私有属性
      const loopSetKey = ({ target, propPath, index = [] }) => {
        let idx = 0;
        for (const row of target) {
          let hasChildren = row[_childrenKey] && row[_childrenKey].length;
          hasFormItem && (row._propPath = (propPath === undefined ? `data.` : `${propPath}.children.`) + idx);
          row._uuid = idx + "-" + Utils.uuid();
          // row._selected = _selection === undefined;
          if (hasChildren) {
            row._index = index.length ? [...index, idx] : [idx];
            loopSetKey({
              target: row[_childrenKey],
              propPath: row._propPath,
              index: row._index,
            });
          } else {
            row._index = idx;
          }
          idx += 1;
          // 添加权限
          if (!_hiddenOperation) {
            this.operationList.length && this.setPermis(row);
          }
        }
      };
      data.length && loopSetKey({ target: data });
    },
    // 解析按钮
    renderOperation() {
      const { config, $attrs } = this;
      const operList = config?.operation?.list || [];
      const operMerge = config?.operation?.merge;
      const metaPermis = this.$route?.meta[defaultConfig.metaPermisKey];
      let btnList = reactive(operList);
      if (metaPermis.length) {
        if (operMerge) {
          btnList = operMerge === "push" ? [...metaPermis, ...operList] : [...operList, ...metaPermis];
        } else {
          btnList = metaPermis;
        }
      }
      if (btnList.length) {
        btnList.forEach((btn) => {
          // 为了兼容 旧系统字段
          "text" in btn && (btn.name = btn.text);
          "showType" in btn && (btn.type = btn.showType);
          "tableName" in btn && (btn.tableKey = btn.tableName);
          "class" in btn && delete btn.class;
          delete btn.text;
          btn.size = btn.size || $attrs.size || "";
          btn.loading = false;
          // 为 element-plus 处理 link text属性
          const btn_attrs = btn.attributes;
          if (btn_attrs && typeof btn_attrs === "string") {
            const attrArr = btn_attrs.split(",");
            for (const key of attrArr) {
              btn[key] = true;
            }
            delete btn.attributes;
          }
        });
        this.operationList = btnList;
      }
    },
    isDateType(type) {
      return dateType.includes(type);
    },
    // 按钮添加权限
    setPermis(row) {
      // 解析并添加权限
      const { _tableKey, currentRowKey, operationList, config, inTableRowBtn, showPermisBtn } = this;
      const analysisBtn = operationList
        .filter((btn) => +btn.inTable !== 2)
        .filter((btn) => {
          const rule = btn.rule;
          rule && rule.includes("storage") && tableJs.matchStorage(rule);
          const _resRule = new Function("row", "storage", "store", `return ${rule || true}`)(row, window.storage, null);
          const resp = _resRule && (!btn.tableKey || _tableKey.includes(btn.tableKey));
          const operation = config.operation;
          // 用于在特定条件下显示隐藏当前行所有操作按钮
          if (operation?.hiddenList) {
            return resp && !operation?.hiddenList(row, _tableKey);
          } else {
            return resp;
          }
        });
      // 设置 每行要显示的按钮
      this.inTableRowBtn[currentRowKey + row[currentRowKey]] = Utils.deepClone(analysisBtn);
      // 设置按钮折叠
      const { showCount } = this.operationOpts;
      for (const key in inTableRowBtn) {
        showPermisBtn[key] = showCount < 0 ? inTableRowBtn[key] : inTableRowBtn[key].slice(0, showCount);
      }
    },
    // 获取字典
    getRemoteDic(dicKey, dicBody) {
      const { getDic } = defaultConfig;
      if (!getDic) throw new Error("no getDic methods in the configuration");
      const params =
        typeof dicBody === "string" || typeof dicBody === "function" ? dicBody : dicBody.value || dicBody.data;
      let method = typeof dicBody === "function" ? eval(dicBody) : getDic;
      method(params)
        .then((res) => this.transferDic(dicKey, { data: res, color: dicBody.color }))
        .catch(() => this.init());
    },
    // 转化 字典 dicConfig
    transferDic(dicKey, dicBody) {
      const { data, color, filter = null } = dicBody;
      // 记录字典执行数量
      this.loadDicNum++;
      // 字典枚举对象 用于模板使用设置内容及样式
      const dicData = filter && typeof filter === "function" ? filter(data) : data || [];
      if (dicData.length == 0) {
        console.error(`字典请求异常:${dicKey}`);
      }
      this.dicEnum[dicKey] = {
        data: Object.fromEntries(
          dicData.map((item) => [item[defaultConfig.dicKeys[1]], item[defaultConfig.dicKeys[0]]])
        ),
        color,
      };
      // 字典枚举数据 用于渲染下拉框 如：{status:[{label:'xx',value:'1'}]}
      this.dicEnumData[dicKey] = dicData;
      if (this.dicLoaded) {
        // 获取表格数据
        this.init();
        this.$emit("dicLoaded", this.dicEnumData);
      }
    },
    // 回调总出口
    dispatchHandle(params) {
      this.$emit("operation", params);
    },
    // 获取接口数据
    requestData({ apiName = null, params = null, model = "search" } = {}) {
      const { config, paging } = this;
      // 搜索模式 current重置第一页
      if (model === "search") {
        paging.current = 1;
      }
      // 有自定义apiName 将覆盖原来的
      if (apiName) {
        config.request.apiName = apiName;
      }
      // 没有配置request 或 apiName 直接终止
      if (!config.request || !config.request.apiName) return;
      const {
        apiName: requestApi,
        params: reqParams = {},
        headers = {},
        formatData = null,
        safeNullParams = false,
      } = config.request;
      if (!requestApi) return;
      const { request, response } = config.page
        ? Utils.deepMerge2(defaultConfig.page, config.page)
        : defaultConfig.page;
      const queryData = Object.assign({}, reqParams, this.searchFormData, params);
      // 将空值删除 开启后将过滤保留空的字段
      tableJs.filterNullParams(queryData, safeNullParams, Utils.getType(safeNullParams));
      // // 参数缓存变量 分页使用
      this.searchParamsCache = queryData;
      // 是否执行v-loading
      if (this.loading || defaultConfig.loading) {
        this.vloading = true;
      }
      // 请求接口
      const apiNameResult = requestApi(
        {
          ...(this.showPagination && {
            [request.size]: paging["page-size"],
            [request.current]: paging.current,
          }),
          ...queryData,
        },
        headers
      );
      // 如果apiNameResult = false 终止以下所有操作(会认为是页面自定义接口)
      if (!apiNameResult) return;
      // 执行接口
      return apiNameResult
        .then(async ({ data }) => {
          let dataList = data[response.records];
          dataList = Array.isArray(data) || !dataList ? data : dataList;
          const dataIsObject = Utils.getType(dataList) === "Object";
          if ((dataIsObject && !dataList[response.records]) || !dataList) {
            this.dataList.length = 0;
            this.vloading = false;
            return;
          }
          // 当 data 为对象 且没有list或records属性时 终止后面操作 必须通过formData处理数据
          if (dataIsObject) {
            if (formatData) {
              this.dataList = formatData(dataList);
            }
            return;
          }
          // 是否 需要处理返回的数据
          if (formatData) {
            // 此处 在各项目中判断不准确，目前建议不要使用async function
            if (["AsyncFunction", "Promise"].includes(Utils.getType(formatData))) {
              dataList = await formatData(dataList);
            } else {
              dataList = formatData(dataList);
            }
          }

          this.dataList = dataList;
          paging.total = data[response.total] || dataList.length;
          this.setPrivateKey({ data: dataList });
          this.emitData(dataList);
          this.vloading = false;
        })
        .catch(() => {
          this.vloading = false;
        });
    },
    /**
     * @param {*} data 设置请求参数
     * @return { Object } 所有set方法
     */
    setParams(data = {}) {
      Object.assign(this.searchFormData, data);
      return this.returnSetFn;
    },
    /**
     * 设置row的某一个数据
     */
    setRow(index, data = null) {
      const target = this.dataList[index];
      tableJs.setProp.call(this, target, data);
      this._tableFormRef && this._tableFormRef.clearValidate();
      return this.returnSetFn;
    },
    /**
     * 设置header列的任意属性 当前版本attrs属性除外
     */
    setHeader(prop, data) {
      const target = this.tbHeader.find((item) => item.prop === prop);
      tableJs.setProp.call(this, target, data);
      return this.returnSetFn;
    },
    /**
     * 设置当前页数据
     */
    setData(data, type = "") {
      if (!Array.isArray(data)) throw new Error("data: Must be an Array");
      if (type && ["push", "unshift"].includes(type)) {
        this.dataList[type](...data);
      } else {
        this.dataList = data;
      }
      !this.operationList.length && this.renderOperation();
      this.emitData(data);
      return this.returnSetFn;
    },
    // 设置分页控件参数
    setPage(data) {
      Object.assign(this.paging, data);
      return this.returnSetFn;
    },
    // 表单验证
    validate(callback) {
      let result = true;
      const { _selection, selectedData, tableData, isValidate } = this;
      if (isValidate || _selection) {
        this._tableFormRef.validate((valid) => {
          result = valid;
        });
      }
      const res = result ? (selectedData.length ? selectedData : tableData) : result;
      if (callback) {
        return callback(res);
      }
      return Promise.resolve(res);
    },
    // 分页操作
    handlePaging(value, type) {
      if (type === "page-size" && this.paging["current"] > 1) this.paging["current"] = 1;
      this.paging[type] = value;
      if (this._hasRequestApi) {
        this.requestData({ params: this.searchParamsCache, model: "" });
      } else {
        this.$emit("operation", { type, value });
      }
    },
    // 设置 selection 首列隐藏 当开启单选模式时有效
    cellClass(data) {
      const columnInfo = data.column;
      if (columnInfo.type === "selection") {
        const hasLabel = columnInfo.label;

        // if (this.singleMode) {
        // 	return hasLabel ? 'has-selection' : 'hidden-selection';
        // }
        if (hasLabel) {
          return "selection-header";
        }
      }
      return tableJs.getResultByPros.call(this, "header-cell-class-name", data);
    },
    // 设置选中背景色
    _rowClassName(data) {
      const rowClassName = tableJs.getResultByPros.call(this, "row-class-name", data);
      if (this.highlightColor) {
        const cutrowKey = this.currentRowKey;
        for (const key of this.selectedData) {
          if (key[cutrowKey] === data.row[cutrowKey]) {
            return `row-highlight-color${" " + rowClassName}`;
          }
        }
        if (this.attrsKeys.includes("highlight-current-row")) {
          return `current-row-color${" " + rowClassName}`;
        }
      }
      return rowClassName.trim();
    },
    onSearch(data) {
      const searchHandle = this.$attrs["search"];
      if (searchHandle) return searchHandle(data);
      this.requestData({ params: data });
    },
    onDownLoad(data) {
      const defaultDownLoad = defaultConfig.download;
      defaultDownLoad ? defaultDownLoad(data) : Utils.downFile(data.row[data.field]);
    },
    uploadSuccess(data, row, item, index) {
      const linkProp = item.linkProp;
      if (linkProp) {
        const findLinkItem = this.tbHeader.find((item) => item.prop === linkProp);
        const resValue = findLinkItem && findLinkItem.type === "img" ? data.fileUrl : data.fileName;
        row[linkProp] ? (row[linkProp] = resValue) : (row[[linkProp]] = resValue);
        return;
      }
      this.dispatchHandle({ type: "upload", data, row, index });
    },
    // 单选
    singleClick(val, row, index, from = "") {
      this.elTableRef.setCurrentRow(row);
      row._selected = !row._selected;
      this.selectedData.shift();
      this.selectedData.push(row);
      if (!from) {
        const hasChildren = row[this._childrenKey] && row[this._childrenKey].length;
        this.$emit("select", {
          row,
          index: hasChildren ? row._index : index,
          tableId: this._tableKey,
        });
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.zs-table-page {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  @mixin flex-align($align: center) {
    display: flex;
    justify-content: $align;
    align-items: $align;
  }

  .zs-table-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }

  .zs-table-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }

  &::after {
    content: var(-selection-text);
  }

  .top-operation {
    & + .zs-table,
    & + .zs-table-content {
      margin-top: 10px;
    }
  }

  .zs-flex-center {
    @include flex-align;
  }

  .zs-table :deep(.cell .zs-radio_check .el-radio__label) {
    display: none;
  }

  .zs-table :deep(.cell) {
    .el-form-item {
      margin-bottom: 0;
    }

    .el-form-item__error {
      position: initial;
      width: 100%;
      text-align: left;
    }

    .el-date-editor.el-input,
    .el-date-editor.el-input__inner {
      width: 100%;
    }

    .table-switch-align > .el-form-item__content {
      @include flex-align;
    }
  }

  :deep(.row-highlight-color),
  :deep(.current-row.current-row-color) {
    td {
      background-color: var(--highlight-color) !important;
    }
  }

  :deep(.hidden-selection),
  :deep(.has-selection) {
    & > .cell {
      label.el-checkbox {
        display: none;
      }
    }
  }

  :deep(.has-selection > .cell) {
    &::after {
      content: var(--selection-text);
      color: var(--header-color);
    }
  }

  :deep(.selection-header) {
    & > .cell .el-checkbox::after {
      content: var(--selection-text);
      padding-left: 4px;
      color: var(--header-color);
      font-weight: bold;
    }
  }

  .zs-table-pagination {
    padding: 10px 0;
    text-align: var(--page-align);
  }

  :deep(.zs-radio-hook .el-radio__input) {
    & .el-radio__inner {
      width: 17px;
      height: 17px;
    }

    & .el-radio__inner::after {
      content: "";
      box-sizing: content-box;
      border: 1px solid #fff;
      border-left: 0;
      border-top: 0;
      height: 7px;
      left: 5px;
      position: absolute;
      top: 2px;
      transform: rotate(45deg) scaleY(0);
      width: 4px;
      border-radius: 0;
      transition: transform 0.06s ease-in;
      transform-origin: center;
      background: none;
    }

    &.is-checked .el-radio__inner::after {
      transform: rotate(45deg) scaleY(1);
    }
  }
}
</style>
