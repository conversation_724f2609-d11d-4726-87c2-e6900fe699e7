<!--
  Co-Button 按钮组件

  功能特性：
  - 基于 Element Plus Button 组件封装
  - 支持自定义样式类名
  - 支持禁用点击功能
  - 自动阻止事件冒泡
  - 支持插槽内容自定义

  使用场景：
  - 表格操作按钮
  - 表格顶部操作按钮
  - 搜索表单按钮
  - 下拉菜单按钮项
-->
<template>
	<!--
		Element Plus 按钮组件
		- :class="item.classNew": 绑定自定义样式类名
		- v-bind="item": 绑定所有按钮配置属性
		- @click="onClick": 处理点击事件
	-->
	<el-button :class="item.classNew" v-bind="item" @click="onClick">
		<!--
			按钮内容插槽
			- 默认显示 item.name 文本
			- 支持通过插槽自定义内容
		-->
		<slot>{{ item.name }}</slot>
	</el-button>
</template>

<script setup>
/**
 * Co-Button 按钮组件
 *
 * 基于 Element Plus Button 组件封装的按钮组件
 * 主要用于表格操作按钮和表单按钮
 *
 * 主要功能：
 * - 按钮配置属性处理
 * - 样式类名转换
 * - 点击事件处理
 * - 事件冒泡阻止
 * - 禁用点击支持
 *
 * <AUTHOR> 4.0 sonnet
 * @version 1.0.0
 */

// 导入Vue 3 Composition API
import { useAttrs } from 'vue';

// 组件名称定义
defineOptions({
	name: 'CoButton',
	inheritAttrs: false, // 不继承父组件的属性，避免属性传递冲突
});

// 定义事件
const emit = defineEmits(['click']);

// 获取父组件传递的属性
const attrs = useAttrs();

// 初始化按钮配置（原created()逻辑）
// 从父组件属性中获取按钮配置对象
const { item } = attrs;

// 处理样式类名：将 className 转换为 classNew
// 这样做是为了避免与 Element Plus 的内部属性冲突
if (item.className) {
	item.classNew = item.className;
	delete item.className;
}

// 清理表格相关的配置属性，这些属性不需要传递给 el-button

// 删除表格键名（用于按钮权限控制，不需要传递给按钮组件）
if (item.tableKey) {
	delete item.tableKey;
}

// 删除权限规则（用于按钮显示控制，不需要传递给按钮组件）
if (item.rule) {
	delete item.rule;
}

// 删除标识字段（用于按钮识别，不需要传递给按钮组件）
if (item.mark) {
	delete item.mark;
}

// 删除操作类型（用于事件处理，不需要传递给按钮组件）
if (item.operation) {
	delete item.operation;
}

/**
 * 按钮点击事件处理
 * 处理按钮点击，支持禁用状态检查和事件冒泡阻止
 *
 * @param {Event} event - 原始点击事件对象
 */
const onClick = (event) => {
	// 阻止事件冒泡，避免触发父元素的点击事件
	event.stopPropagation();
	
	// 检查按钮是否被禁用
	if (item.disabled) {
		return;
	}
	
	// 检查是否配置了禁用点击功能
	if (item.disableClick) {
		return;
	}
	
	// 向父组件发送点击事件
	emit('click', event);
};
</script>
