<!--
  Static-Component 静态组件

  这是一个无模板的渲染函数组件，用于根据配置动态渲染不同类型的静态内容

  功能特性：
  - 基于渲染函数的动态组件
  - 支持多种内容类型渲染
  - 支持自定义样式和格式化
  - 支持交互事件处理
  - 策略模式的类型渲染

  支持的类型：
  - date: 日期时间显示
  - download: 下载链接
  - preview: 预览链接
  - img: 图片显示
  - enum: 枚举值显示
  - default: 默认文本显示

  使用场景：
  - 表格单元格内容渲染
  - 静态数据展示
  - 需要格式化的内容显示
-->

<script setup>
/**
 * Static-Component 静态组件
 *
 * 基于 Vue 3 渲染函数的动态内容渲染组件
 * 使用策略模式根据类型配置渲染不同的内容
 *
 * 主要功能：
 * - 动态类型渲染
 * - 样式和格式化支持
 * - 交互事件处理
 * - 数据格式化
 * - 空值处理
 *
 * <AUTHOR> 4.0 sonnet
 * @version 1.0.0
 */

// 导入Vue 3 渲染函数和Composition API
import { h, useAttrs } from 'vue';
// 导入工具类
import Utils from "../utils";

// 组件名称定义
defineOptions({
	name: 'StaticComponent',
	inheritAttrs: false, // 不继承父组件的属性
});

// 获取父组件传递的属性
const attrs = useAttrs();

/**
 * 渲染策略对象
 * 使用策略模式定义不同类型内容的渲染方法
 */
const renderStrategies = {
	/**
	 * 日期时间类型渲染
	 * @param {*} value - 日期时间值
	 * @param {Object} item - 配置项
	 * @returns {VNode} 渲染的虚拟节点
	 */
	date: (value, item) => {
		if (!value) return h('span', '-');

		// 使用工具类格式化日期时间
		const formattedDate = Utils.formatDate ? Utils.formatDate(value, item.format) : value;
		return h('span', formattedDate);
	},

	/**
	 * 下载链接类型渲染
	 * @param {*} value - 下载链接或文件信息
	 * @param {Object} item - 配置项
	 * @returns {VNode} 渲染的虚拟节点
	 */
	download: (value, item) => {
		if (!value) return h('span', '-');

		return h('a', {
			href: value,
			download: item.filename || true,
			class: 'download-link',
			style: item.style || {},
		}, item.text || '下载');
	},

	/**
	 * 预览链接类型渲染
	 * @param {*} value - 预览链接
	 * @param {Object} item - 配置项
	 * @returns {VNode} 渲染的虚拟节点
	 */
	preview: (value, item) => {
		if (!value) return h('span', '-');

		return h('a', {
			href: value,
			target: '_blank',
			class: 'preview-link',
			style: item.style || {},
		}, item.text || '预览');
	},

	/**
	 * 图片类型渲染
	 * @param {*} value - 图片链接
	 * @param {Object} item - 配置项
	 * @returns {VNode} 渲染的虚拟节点
	 */
	img: (value, item) => {
		if (!value) return h('span', '-');

		return h('img', {
			src: value,
			alt: item.alt || '',
			class: 'static-img',
			style: {
				maxWidth: '100px',
				maxHeight: '100px',
				...item.style
			},
			onError: (e) => {
				e.target.src = item.errorImg || '';
			}
		});
	},

	/**
	 * 枚举值类型渲染
	 * @param {*} value - 枚举值
	 * @param {Object} item - 配置项
	 * @returns {VNode} 渲染的虚拟节点
	 */
	enum: (value, item) => {
		if (value === null || value === undefined) return h('span', '-');

		// 从枚举映射中查找对应的显示文本
		const enumMap = item.enumMap || {};
		const displayText = enumMap[value] || value;

		return h('span', {
			class: `enum-${value}`,
			style: item.style || {},
		}, displayText);
	},

	/**
	 * 默认类型渲染
	 * @param {*} value - 要显示的值
	 * @param {Object} item - 配置项
	 * @returns {VNode} 渲染的虚拟节点
	 */
	default: (value, item) => {
		// 处理空值
		if (value === null || value === undefined || value === '') {
			return h('span', item.emptyText || '-');
		}

		// 处理数字格式化
		if (typeof value === 'number' && item.format) {
			const formattedValue = Utils.formatNumber ? Utils.formatNumber(value, item.format) : value;
			return h('span', {
				class: 'number-value',
				style: item.style || {},
			}, formattedValue);
		}

		// 默认文本渲染
		return h('span', {
			class: item.className || '',
			style: item.style || {},
		}, String(value));
	}
};

/**
 * 渲染函数
 * 根据配置动态选择渲染策略
 *
 * @returns {VNode} 渲染的虚拟节点
 */
const render = () => {
	// 从属性中获取配置信息
	const { item, row, value: propValue } = attrs;

	// 确定要渲染的值
	const value = propValue !== undefined ? propValue : (row && item.prop ? row[item.prop] : null);

	// 获取渲染类型，默认为 'default'
	const type = item.type || 'default';

	// 选择对应的渲染策略
	const strategy = renderStrategies[type] || renderStrategies.default;

	// 执行渲染策略
	try {
		return strategy(value, item);
	} catch (error) {
		console.error(`StaticComponent render error for type "${type}":`, error);
		// 渲染出错时显示错误信息
		return h('span', { class: 'render-error' }, '渲染错误');
	}
};

// 导出渲染函数（Vue 3 setup 组件的渲染函数）
defineExpose({ render }); 
</script>

<script>
// 由于这是一个渲染函数组件，需要在这里定义render选项
export default {
	render() {
		// 调用setup中定义的render函数
		return this.render();
	}
};
</script>
