<!--
  Co-Container 容器组件

  功能特性：
  - 智能容器类型选择
  - 根据内容类型自动选择合适的容器
  - 当包含表单项时使用 el-form 容器，否则使用普通模板
  - 支持表单验证功能
  - 性能优化（避免不必要的表单包装）

  使用场景：
  - 表格内表单容器
  - 搜索表单容器
  - 需要条件性表单包装的场景
-->
<template>
	<!-- 条件渲染：根据是否包含表单项选择容器类型 -->
	
	<!-- 表单容器：当包含表单项时使用 -->
	<el-form
		v-if="hasFormItem"
		ref="formRef"
		:model="model"
		v-bind="$attrs"
		:size="configOpts?.size || 'default'"
	>
		<!-- 插槽：渲染表单内容 -->
		<slot />
	</el-form>
	
	<!-- 普通容器：当不包含表单项时使用 -->
	<template v-else>
		<!-- 插槽：直接渲染内容，不包装表单 -->
		<slot />
	</template>
</template>

<script setup>
/**
 * Co-Container 容器组件
 *
 * 智能容器组件，根据内容类型自动选择合适的容器
 * 当包含表单项时使用 el-form 容器，否则使用普通模板
 *
 * 主要功能：
 * - 智能容器类型选择
 * - 表单验证支持
 * - 表单引用提供
 * - 性能优化（避免不必要的表单包装）
 *
 * <AUTHOR> 4.0 sonnet
 * @version 1.0.0
 */

// 导入 Vue 3 Composition API
import { ref } from 'vue';

// 组件名称定义
defineOptions({
	name: 'CoContainer',
	inheritAttrs: false, // 不继承父组件的属性
});

/**
 * 组件属性定义
 */
defineProps({
	// 表单数据模型，用于表单验证和数据绑定
	model: Object,
	
	// 是否包含表单项，决定使用哪种容器类型
	hasFormItem: Boolean,
	
	// 配置选项对象，包含尺寸等配置
	configOpts: Object,
});

// 表单引用，用于访问表单方法
const formRef = ref(null);

/**
 * 获取表单引用
 * 提供对内部 el-form 组件的访问，用于表单验证等操作
 *
 * @returns {Object|null} 表单组件实例或null（当不是表单容器时）
 */
const getFormRef = () => {
	return formRef.value;
};

/**
 * 验证表单
 * 对表单进行完整验证
 *
 * @returns {Promise} 验证结果的Promise
 */
const validate = () => {
	if (!formRef.value) {
		return Promise.resolve(true);
	}
	return formRef.value.validate();
};

/**
 * 验证指定字段
 * 对表单中的指定字段进行验证
 *
 * @param {string|Array} props - 要验证的字段名或字段名数组
 * @returns {Promise} 验证结果的Promise
 */
const validateField = (props) => {
	if (!formRef.value) {
		return Promise.resolve(true);
	}
	return formRef.value.validateField(props);
};

/**
 * 重置表单
 * 重置表单的所有字段，清除验证结果
 */
const resetFields = () => {
	if (formRef.value) {
		formRef.value.resetFields();
	}
};

/**
 * 清除验证结果
 * 清除表单的验证结果，但不重置字段值
 *
 * @param {string|Array} props - 要清除验证的字段名或字段名数组，不传则清除所有
 */
const clearValidate = (props) => {
	if (formRef.value) {
		formRef.value.clearValidate(props);
	}
};

// 向父组件暴露方法
defineExpose({
	getFormRef,
	validate,
	validateField,
	resetFields,
	clearValidate,
});
</script>
