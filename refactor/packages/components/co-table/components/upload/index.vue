<!--
  Co-Upload 文件上传组件

  功能特性：
  - 基于 Element Plus Upload 组件封装
  - 支持文件类型验证
  - 支持文件大小限制
  - 支持自定义上传方法
  - 支持上传状态显示
  - 单文件上传模式

  使用场景：
  - 表格内文件上传
  - 表单文件上传
  - 需要文件验证的上传场景
-->
<template>
	<!--
		Element Plus 文件上传组件
		- class="upload-demo": 应用上传组件样式
		- v-bind="_attrs": 绑定过滤后的属性
		- action="": 上传地址（使用自定义请求方法，所以为空）
		- :multiple="false": 禁用多文件选择
		- list-type="text": 文件列表显示类型为文本
		- :auto-upload="true": 启用自动上传
		- :limit="1": 限制最多上传1个文件
		- :drag="false": 禁用拖拽上传
		- :show-file-list="false": 隐藏文件列表
		- :disabled: 根据上传状态和配置决定是否禁用
		- :before-upload: 上传前的验证处理
		- :http-request: 自定义上传请求方法
	-->
	<el-upload
		class="upload-demo"
		v-bind="_attrs"
		action=""
		:multiple="false"
		list-type="text"
		:auto-upload="true"
		:limit="1"
		:drag="false"
		:show-file-list="false"
		:disabled="uploading||_attrs.disabled"
		:before-upload="file=>beforeUpload(file,_attrs)"
		:http-request="httpRequest"
	>
		<!--
			上传按钮
			- v-if: 当样式配置为字符串时显示按钮
			- type="text": 文本类型按钮
			- :[attrName]: 动态绑定样式属性（class 或 style）
			- :loading: 显示上传加载状态
		-->
		<el-button
			v-if="typeof _attrs.styles==='string'"
			type="text"
			:[attrName]="_attrs.styles"
			:loading="uploading"
		>
			{{ _attrs.text||'上传文件' }}
		</el-button>
	</el-upload>
</template>

<script>
/**
 * Co-Upload 文件上传组件
 *
 * 基于 Element Plus Upload 组件封装的文件上传组件
 * 支持文件类型验证、大小限制和自定义上传方法
 *
 * 主要功能：
 * - 文件类型验证
 * - 文件大小限制
 * - 自定义上传方法
 * - 上传状态管理
 * - 属性过滤处理
 *
 * <AUTHOR> 4.0 sonnet
 * @version 1.0.0
 */

// 导入默认配置
import defaultConfig from '../../config.js'
// 导入工具类
import Utils from '../../utils'

export default {
	name: 'CoUpload',

	/**
	 * 组件数据
	 * @returns {Object} 组件的响应式数据
	 */
	data() {
		return {
			// 上传状态标识，用于显示加载状态和禁用按钮
			uploading: false
		}
	},

	computed: {
		/**
		 * 过滤后的属性对象
		 * 移除组件内部使用的属性，只保留传递给 el-upload 的属性
		 *
		 * @param {Object} $attrs - 父组件传递的所有属性
		 * @returns {Object} 过滤后的属性对象
		 */
		_attrs({ $attrs }) {
			// 使用解构赋值和剩余参数，移除内部使用的属性
			return (({ methodFn, linkProps, ...other }) => (other))($attrs)
		}
	},

	/**
	 * 组件创建时的初始化逻辑
	 * 根据样式配置确定属性绑定方式
	 */
	created() {
		// 获取样式配置
		const attrsStyle = this._attrs.styles

		// 根据样式配置类型确定属性名
		// 如果 styles 是字符串，使用 class 属性；如果是对象，使用 style 属性
		this.attrName = attrsStyle
			? typeof this._attrs.styles === 'string'
				? 'class'   // 字符串样式使用 class 属性
				: 'style'   // 对象样式使用 style 属性
			: 'class'       // 默认使用 class 属性
	},
	methods: {
		/**
		 * 自定义上传请求方法
		 * 替代默认的上传行为，使用配置的上传方法处理文件
		 *
		 * @param {Object} data - 上传数据对象，包含文件信息
		 *
		 * @description
		 * 该方法会：
		 * 1. 获取上传方法（优先使用自定义方法，其次使用全局配置）
		 * 2. 验证上传方法的有效性
		 * 3. 执行上传操作
		 * 4. 处理上传结果和状态
		 */
		httpRequest(data) {
			// 获取全局配置的上传方法
			const uploadMethod = defaultConfig.upload

			// 优先使用组件传入的自定义上传方法，其次使用全局配置的方法
			const uploadFn = this.$attrs.methodFn || uploadMethod

			// 验证上传方法是否存在
			if (!uploadFn) {
				throw new Error(`upload: global upload and the custom upload at least one`)
			}

			// 验证上传方法是否为函数类型
			if (!uploadFn || !Utils.getType(uploadFn) === 'Function') {
				throw new Error(`upload: parameter is wrong, should be function`)
			}

			// 执行上传操作
			uploadFn(data).then(res => {
				// 上传成功，向父组件发送成功事件
				this.$emit('onSuccess', res)
			}).finally(_ => {
				// 无论成功还是失败，都要重置上传状态
				this.uploading = false
			})
		},

		/**
		 * 上传前的文件验证方法
		 * 验证文件类型和大小是否符合要求
		 *
		 * @param {File} file - 要上传的文件对象
		 * @param {Object} attrs - 上传配置属性
		 * @returns {boolean} 验证通过返回 true，否则返回 false
		 *
		 * @description
		 * 该方法会验证：
		 * 1. 文件类型是否在允许的类型列表中
		 * 2. 文件大小是否超过限制
		 */
		beforeUpload(file, attrs) {
			// 提取文件扩展名（包含点号，如：'.jpg'）
			const extFileName = file.name.substring(file.name.lastIndexOf('.'))

			// 解析允许的文件类型列表
			const uploadFileTypes = attrs.accept.split(',')

			// 验证文件类型
			if (uploadFileTypes.length > 0) {
				if (!uploadFileTypes.includes(extFileName)) {
					// 文件类型不支持，显示错误消息并阻止上传
					this.$message.error('不支持的文件类型')
					return false
				}
			}

			// 验证文件大小
			const _maxSize = attrs.size || 10; // 默认最大10MB
			const fileSizeCheckResult = file.size / 1024 / 1024 <= _maxSize

			if (!fileSizeCheckResult) {
				// 文件大小超限，显示错误消息并阻止上传
				this.$message.error(`已超出文件大小，不能大于(${_maxSize}MB)`)
				return false
			}

			// 验证通过，设置上传状态并允许上传
			this.uploading = true
			return true
		}
	}
}
</script>
