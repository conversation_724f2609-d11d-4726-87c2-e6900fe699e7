<!--
  Co-Upload 文件上传组件

  功能特性：
  - 基于 Element Plus Upload 组件封装
  - 支持文件类型验证
  - 支持文件大小限制
  - 支持自定义上传方法
  - 支持上传状态显示
  - 单文件上传模式

  使用场景：
  - 表格内文件上传
  - 表单文件上传
  - 需要文件验证的上传场景
-->
<template>
	<!--
		Element Plus 文件上传组件
		- class="upload-demo": 应用上传组件样式
		- v-bind="_attrs": 绑定过滤后的属性
		- action="": 上传地址（使用自定义请求方法，所以为空）
		- :multiple="false": 禁用多文件选择
		- list-type="text": 文件列表显示类型为文本
		- :auto-upload="true": 启用自动上传
		- :limit="1": 限制最多上传1个文件
		- :drag="false": 禁用拖拽上传
		- :show-file-list="false": 隐藏文件列表
		- :disabled: 根据上传状态和配置决定是否禁用
		- :before-upload: 上传前的验证处理
		- :http-request: 自定义上传请求方法
	-->
	<el-upload
		class="upload-demo"
		v-bind="_attrs"
		action=""
		:multiple="false"
		list-type="text"
		:auto-upload="true"
		:limit="1"
		:drag="false"
		:show-file-list="false"
		:disabled="uploading || _attrs.disabled"
		:before-upload="(file) => beforeUpload(file, _attrs)"
		:http-request="httpRequest"
	>
		<!--
			上传按钮
			- v-if: 当样式配置为字符串时显示按钮
			- type="text": 文本类型按钮
			- :[attrName]: 动态绑定样式属性（class 或 style）
			- :loading: 显示上传加载状态
		-->
		<el-button
			v-if="typeof _attrs.styles === 'string'"
			type="text"
			:[attrName]="_attrs.styles"
			:loading="uploading"
		>
			{{ _attrs.text || '上传文件' }}
		</el-button>
	</el-upload>
</template>

<script setup>
/**
 * Co-Upload 文件上传组件
 *
 * 基于 Element Plus Upload 组件封装的文件上传组件
 * 支持文件类型验证、大小限制和自定义上传方法
 *
 * 主要功能：
 * - 文件类型验证
 * - 文件大小限制
 * - 自定义上传方法
 * - 上传状态管理
 * - 属性过滤处理
 *
 * <AUTHOR> 4.0 sonnet
 * @version 1.0.0
 */

// 导入Vue 3 Composition API
import { ref, computed, useAttrs } from 'vue';
// 导入默认配置和工具类
import defaultConfig from '../../config.js';
import Utils from '../../utils';

// 组件名称定义
defineOptions({
	name: 'CoUpload',
	inheritAttrs: false, // 不继承父组件的属性，避免属性传递冲突
});

// 获取父组件传递的属性
const attrs = useAttrs();

// 响应式数据定义
const uploading = ref(false); // 上传状态标识，用于显示加载状态和禁用按钮
const attrName = ref('class'); // 样式属性名，默认为class

// 计算过滤后的属性对象（原computed逻辑）
// 移除组件内部使用的属性，只保留传递给 el-upload 的属性
const _attrs = computed(() => {
	const { methodFn, linkProps, ...other } = attrs;
	return other;
});

// 初始化逻辑（原created()逻辑）
// 根据样式配置确定属性绑定方式
const attrsStyle = _attrs.value.styles;

// 根据样式配置类型确定属性名
// 如果 styles 是字符串，使用 class 属性；如果是对象，使用 style 属性
attrName.value = attrsStyle
	? typeof _attrs.value.styles === 'string'
		? 'class'   // 字符串样式使用 class 属性
		: 'style'   // 对象样式使用 style 属性
	: 'class';      // 默认使用 class 属性

/**
 * 自定义上传请求方法
 * 替代默认的上传行为，使用配置的上传方法处理文件
 *
 * @param {Object} data - 上传数据对象，包含文件信息
 *
 * @description
 * 该方法会：
 * 1. 获取上传方法（优先使用自定义方法，其次使用全局配置）
 * 2. 验证上传方法的有效性
 * 3. 执行上传操作
 * 4. 处理上传结果和状态
 */
const httpRequest = (data) => {
	// 获取全局配置的上传方法
	const uploadMethod = defaultConfig.upload;
	
	// 优先使用组件传入的自定义上传方法，其次使用全局配置的方法
	const uploadFn = attrs.methodFn || uploadMethod;
	
	// 验证上传方法是否存在
	if (!uploadFn) {
		throw new Error(`upload: global upload and the custom upload at least one`);
	}
	
	// 验证上传方法是否为函数类型
	if (!uploadFn || Utils.getType(uploadFn) !== 'Function') {
		throw new Error(`upload: parameter is wrong, should be function`);
	}
	
	// 设置上传状态为进行中
	uploading.value = true;
	
	// 执行上传操作
	uploadFn(data)
		.then((res) => {
			// 上传成功处理
			console.log('上传成功:', res);
			// 这里可以添加成功回调逻辑
		})
		.catch((error) => {
			// 上传失败处理
			console.error('上传失败:', error);
			// 这里可以添加失败回调逻辑
		})
		.finally(() => {
			// 无论成功失败都重置上传状态
			uploading.value = false;
		});
};

/**
 * 上传前的文件验证处理
 * 在文件上传前进行类型和大小验证
 *
 * @param {File} file - 要上传的文件对象
 * @param {Object} attrs - 组件属性配置
 * @returns {boolean} 验证结果，true表示通过验证，false表示验证失败
 *
 * @description
 * 该方法会验证：
 * 1. 文件类型是否符合要求
 * 2. 文件大小是否在限制范围内
 */
const beforeUpload = (file, attrs) => {
	// 获取文件类型限制配置
	const { accept, maxSize } = attrs;
	
	// 文件类型验证
	if (accept) {
		const fileType = file.type;
		const fileName = file.name;
		const fileExtension = fileName.substring(fileName.lastIndexOf('.'));
		
		// 检查文件类型或扩展名是否在允许列表中
		const acceptList = accept.split(',').map(item => item.trim());
		const isValidType = acceptList.some(acceptItem => {
			return fileType.includes(acceptItem) || acceptItem === fileExtension;
		});
		
		if (!isValidType) {
			console.error(`文件类型不符合要求，允许的类型：${accept}`);
			return false;
		}
	}
	
	// 文件大小验证
	if (maxSize) {
		const fileSizeMB = file.size / 1024 / 1024;
		if (fileSizeMB > maxSize) {
			console.error(`文件大小超出限制，最大允许：${maxSize}MB`);
			return false;
		}
	}
	
	// 验证通过
	return true;
};
</script>
