<!--
  Co-Date 日期时间组件

  功能特性：
  - 基于 Element Plus DatePicker/TimePicker 组件封装
  - 支持多种日期时间类型
  - 支持前置下拉选择器
  - 支持分割字段功能
  - 自动生成占位符文本

  使用场景：
  - 表格内日期时间选择
  - 搜索表单日期范围选择
  - 需要分割字段的日期时间输入
-->
<template>
	<!--
		日期时间组件容器
		- :class: 根据是否有前置插槽动态添加样式类
	-->
	<div :class="item.prepend && 'el-input-group el-input--suffix el-input-group--prepend'">
		<!-- 前置下拉选择器 -->
		<div class="el-input-group__prepend" v-if="item.prepend">
			<!--
				前置选择器组件
				- v-bind: 绑定前置选择器配置
				- :row: 传递表单数据模型
				- @change: 监听前置选择器变化事件
			-->
			<co-select 
				v-bind="{ 
					item: item.prepend, 
					dic: $attrs.dic, 
					mainProp: item.prepend.prop ? '' : item.prop 
				}" 
				:row="formModel" 
				@change="(data) => $emit('change', { prop: data.prop, value: data.value })" 
			/>
		</div>
		
		<!--
			动态日期时间组件
			- :is: 根据配置动态选择组件类型
			- v-model: 双向绑定日期时间值
			- v-bind="itemAttrs": 绑定所有配置属性
			- :placeholder: 动态生成占位符文本
			- v-on="listeners": 绑定所有事件监听器
			- @change: 监听日期时间变化事件
		-->
		<component 
			:is="comName" 
			v-model="formModel[item.prop]" 
			v-bind="itemAttrs" 
			:placeholder="itemAttrs.placeholder || '请选择' + (itemAttrs.label || '')" 
			v-on="listeners" 
			@change="onChangeDate($event, item, splitPropData, $attrs, _inTable)" 
		/>
	</div>
</template>

<script setup>
/**
 * Co-Date 日期时间组件
 *
 * 基于 Element Plus DatePicker/TimePicker 组件封装的日期时间组件
 * 支持表格内编辑和搜索表单两种使用场景
 *
 * 主要功能：
 * - 响应式数据绑定
 * - 多种日期时间类型支持
 * - 前置选择器支持
 * - 分割字段功能
 * - 事件统一处理
 * - 字段重置功能
 *
 * <AUTHOR> 4.0 sonnet
 * @version 1.0.0
 */

// 导入Vue 3 Composition API
import { ref, reactive, inject, useAttrs } from 'vue';
// 导入工具类和配置
import Utils from '../../utils';
import { dateType } from '../../config';
// 导入公共事件处理函数
import { handleFn } from './common.js';
// 导入下拉选择器组件
import coSelect from './select.vue';

// 组件名称定义
defineOptions({
	name: 'CoDate',
	inheritAttrs: false, // 不继承父组件的属性，避免属性传递冲突
});

// 注册子组件
defineOptions({
	components: {
		coSelect,
	},
});

// 定义事件
const emit = defineEmits(['change']);

// 获取父组件传递的属性
const attrs = useAttrs();

// 注入父级提供的 widgetItem 对象，用于组件间通信
const widgetItem = inject('widgetItem');

// 初始化组件数据（原data()逻辑）
const { item, scene } = attrs;
const _inTable = scene === 'inTable'; // 是否在表格内使用

// 表单项属性配置，默认支持清空
const itemAttrs = reactive(Object.assign(item.attrs || {}, { clearable: true }));

// 判断是否为时间类型组件
const isTimeType = item.type === 'time';
const hasPickerOpts = isTimeType && itemAttrs['pickerOptions'];

// 设置日期时间组件的类型属性
if (!isTimeType) {
	itemAttrs.type = item.type;
}

// 响应式数据定义
const splitPropData = reactive({}); // 分割字段数据
const resetNum = ref(0); // 重置计数器
const listeners = reactive({}); // 事件监听器对象

// 根据配置确定使用的组件名称
const comName = ref(
	isTimeType && !hasPickerOpts 
		? 'el-time-picker' 
		: hasPickerOpts 
			? 'el-time-select' 
			: 'el-date-picker'
);

// 初始化表单数据模型（原created()逻辑）
const { data = null, row = data } = attrs;
const formModel = reactive(row); // 表单数据模型（响应式）

// 判断是否为日期相关组件
const isDateType = dateType.includes(item.type);

// 初始化事件监听器
if (_inTable && item.events) {
	if (item.events.blur) {
		listeners['blur'] = () => handleFn.call({ formModel, item, _inTable }, 'blur', formModel[item.prop], _inTable, attrs);
	}
}

// 初始化字段数据和注册组件实例
if (item.splitProp && !item.prepend) {
	// 分割字段模式
	formModel[item.splitProp[0]] = '';
	formModel[item.splitProp[1]] = '';
	widgetItem[item.splitProp[0]] = { resetField };
	widgetItem[item.splitProp[1]] = { resetField };
} else {
	// 普通字段模式
	formModel[item.prop] = '';
	widgetItem[item.prop] = { resetField };
}

/**
 * 重置字段方法
 * 恢复字段到指定值或初始状态
 *
 * @param {*} value - 要设置的值
 */
function resetField(value) {
	const splitProp = item.splitProp;
	const itemProp = item.prop;
	
	if (value) {
		if (Utils.getType(value) === 'String') {
			const hasComma = value.includes(',');
			let echoValue = value;
			
			// 处理逗号分隔的日期范围值
			if (hasComma) {
				echoValue = value.split(',');
				formModel[itemProp] = echoValue;
				if (splitProp) {
					splitPropData[splitProp[0]] = echoValue[0];
					splitPropData[splitProp[1]] = echoValue[1];
				}
			} else {
				// 单个日期值
				formModel[itemProp] = echoValue;
				if (splitProp) {
					splitPropData[splitProp[0]] = echoValue;
					splitPropData[splitProp[1]] = '';
				}
			}
		} else {
			// 非字符串类型直接赋值
			formModel[itemProp] = value;
		}
	} else {
		// 重置为空值
		formModel[itemProp] = '';
		if (splitProp) {
			splitPropData[splitProp[0]] = '';
			splitPropData[splitProp[1]] = '';
		}
	}
}

/**
 * 日期时间变化事件处理
 * 处理日期时间组件值变化，支持分割字段功能
 *
 * @param {*} value - 新的日期时间值
 * @param {Object} item - 表单项配置
 * @param {Object} data - 分割字段数据对象
 * @param {Object} $attrs - 组件属性
 * @param {boolean} inTable - 是否在表格内使用
 */
const onChangeDate = (value, item, data, $attrs, inTable) => {
	// 获取分割字段配置
	const hasSplit = item.splitProp;
	
	if (value) {
		const isArrVal = Array.isArray(value);
		
		// 处理数组值且有分割字段的情况
		if (isArrVal && Utils.getType(hasSplit) === 'Array') {
			if (!item.prepend) {
				data[hasSplit[0]] = value[0];
				data[hasSplit[1]] = value[1];
			}
		} else {
			// 处理普通值或数组值的默认拼接
			data[item.prop] = isArrVal && !item.prepend ? value.join(hasSplit) : value;
		}
	} else {
		// 清空值的处理
		formModel[item.prop] = value;
		if (hasSplit) {
			if (!item.prepend) {
				data[hasSplit[0]] = '';
				data[hasSplit[1]] = '';
			}
		} else {
			data[item.prop] = '';
		}
	}
	
	// 触发统一事件处理
	handleFn.call({ formModel, item, _inTable }, 'change', hasSplit ? data : data[item.prop], inTable, $attrs);
};
</script>

<style>
:root {
	--el-input-border-color: var(--el-border-color);
}
</style>
