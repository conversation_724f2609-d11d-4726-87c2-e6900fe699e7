<template>
	<div :class="item.prepend && 'el-input-group el-input--suffix el-input-group--prepend'">
		<div class="el-input-group__prepend" v-if="item.prepend">
			<co-select v-bind="{ item: item.prepend, dic: $attrs.dic, mainProp: item.prepend.prop ? '' : item.prop }" :row="formModel" @change="(data) => $emit('change', { prop: data.prop, value: data.value })" />
		</div>
		<component :is="comName" v-model="formModel[item.prop]" v-bind="itemAttrs" :placeholder="itemAttrs.placeholder || '请选择' + (itemAttrs.label || '')" v-on="listeners" @change="onChangeDate($event, item, splitPropData, $attrs, _inTable)" />
	</div>
</template>
<script>
import Utils from '../../utils';
import { handleFn } from './common.js';
import coSelect from './select.vue';
import { dateType } from '../../config';
export default {
	name: 'CoDate',
	inheritAttrs: false,
	components: {
		coSelect,
	},
	data() {
		const { item, scene } = this.$attrs;
		this._inTable = scene === 'inTable';
		this.itemAttrs = Object.assign(item.attrs || {}, { clearable: true });
		const isTimeType = (this.isTimeType = item.type === 'time');
		const hasPickerOpts = isTimeType && this.itemAttrs['pickerOptions'];
		!isTimeType && (this.itemAttrs.type = item.type);
		return {
			splitPropData: {},
			resetNum: 0,
			item,
			listeners: {},
			comName: isTimeType && !hasPickerOpts ? 'el-time-picker' : hasPickerOpts ? 'el-time-select' : 'el-date-picker',
		};
	},
	inject: ['widgetItem'],
	created() {
		const {
			_inTable,
			listeners,
			item,
			$attrs: { data = null, row = data },
		} = this;
		this.formModel = reactive(row);
		// 是否是时间相关组件
		this.isDateType = dateType.includes(item.type);
		// 初始化数据
		if (_inTable && item.events) {
			item.events.blur && (listeners['blur'] = () => handleFn.call(this, 'blur', this.formModel[item.prop], _inTable, this.$attrs));
		}
		// 将页面vue实例 放入widgetItem中
		if (item.splitProp && !item.prepend) {
			this.formModel[item.splitProp[0]] = '';
			this.formModel[item.splitProp[1]] = '';
			this.widgetItem[item.splitProp[0]] = this;
			this.widgetItem[item.splitProp[1]] = this;
		} else {
			this.formModel[item.prop] = '';
			this.widgetItem[item.prop] = this;
		}
	},
	methods: {
		// 重置
		resetField(value) {
			const splitProp = this.splitProp;
			const itemProp = this.item.prop;
			if (value) {
				if (Utils.getType(value) === 'String') {
					const hasComma = value.includes(',');
					let echoValue = value;
					// 有逗号，2023-07-07,2023-08-01
					if (hasComma) {
						echoValue = value.split(',');
						this.formModel[itemProp] = echoValue;
						if (splitProp) {
							this.splitPropData[splitProp[0]] = echoValue[0];
							this.splitPropData[splitProp[1]] = echoValue[1];
						}
					} else {
						// 无逗号，2023-07-07
						if (splitProp) {
							if (this.resetNum < 1) {
								this.formModel[itemProp] = [value];
							} else {
								this.formModel[itemProp] = [...this.formModel[itemProp], value];
							}
							this.splitPropData[splitProp[0]] = echoValue[0];
							this.splitPropData[splitProp[1]] = echoValue[1];
						} else {
							this.formModel[itemProp] = echoValue;
						}
					}
				}
			} else {
				this.formModel[itemProp] = value;
				if (splitProp) {
					this.splitPropData[splitProp[0]] = value;
					this.splitPropData[splitProp[1]] = value;
				}
			}
			// 解决: 分为两个字段时的赋值，因为是同一个组件，需要两次赋值，resetField执行两次，记录执行次数
			if (splitProp) {
				value ? (this.resetNum += 1) : (this.resetNum = 0);
			}
		},
		onChangeDate(value, item, data, $attrs, inTable) {
			// 分割字段
			const hasSplit = item.splitProp;
			if (value) {
				const isArrVal = Array.isArray(value);
				// 有分割字段 && 数组格式
				if (isArrVal && Utils.getType(hasSplit) === 'Array') {
					if (!item.prepend) {
						data[hasSplit[0]] = value[0];
						data[hasSplit[1]] = value[1];
					}
					// delete data[item.prop];
				} else {
					// 默认 逗号 拼接
					data[item.prop] = isArrVal && !item.prepend ? value.join(hasSplit) : value;
				}
			} else {
				this.formModel[item.prop] = value;
				if (hasSplit) {
					if (!item.prepend) {
						data[hasSplit[0]] = '';
						data[hasSplit[1]] = '';
					}
				} else {
					data[item.prop] = '';
				}
			}
			handleFn.call(this, 'change', hasSplit ? data : data[item.prop], inTable, $attrs);
		},
	},
};
</script>
<style>
:root {
	--el-input-border-color: var(--el-border-color);
}
</style>
