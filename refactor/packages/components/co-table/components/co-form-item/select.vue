<!--
  Co-Select 下拉选择器组件

  功能特性：
  - 基于 Element Plus Select 组件封装
  - 支持字典数据动态加载
  - 支持多选模式
  - 支持选项过滤
  - 自动生成占位符文本

  使用场景：
  - 表格内下拉选择
  - 搜索表单选择项
  - 需要字典数据的选择器
-->
<template>
	<!--
		Element Plus 下拉选择器组件
		- v-model: 双向绑定选中值
		- clearable: 支持清空选择
		- :placeholder: 动态生成占位符文本
		- v-bind="itemAttrs": 绑定所有配置属性
		- v-on="listeners": 绑定所有事件监听器
	-->
	<el-select 
		v-model="formModel[item.prop]" 
		clearable 
		:placeholder="itemAttrs.placeholder || '请选择' + (itemAttrs.label || '')" 
		v-bind="itemAttrs" 
		v-on="listeners"
	>
		<!--
			选项列表
			- v-for: 遍历选项数据
			- :key: 使用值字段作为唯一标识
			- :label: 显示文本
			- :value: 选项值
			- :disabled: 禁用状态
		-->
		<el-option 
			v-for="opt in optionList" 
			:key="opt[valueKey]" 
			:label="opt[labelKey]" 
			:value="opt[valueKey]" 
			:disabled="itemAttrs.disabled" 
		/>
	</el-select>
</template>

<script setup>
/**
 * Co-Select 下拉选择器组件
 *
 * 基于 Element Plus Select 组件封装的下拉选择器
 * 支持表格内编辑和搜索表单两种使用场景
 *
 * 主要功能：
 * - 响应式数据绑定
 * - 字典数据支持
 * - 多选模式支持
 * - 选项过滤功能
 * - 事件统一处理
 * - 字段重置功能
 *
 * <AUTHOR> 4.0 sonnet
 * @version 1.0.0
 */

// 导入Vue 3 Composition API
import { ref, reactive, inject, useAttrs, watch } from 'vue';
// 导入工具类和配置
import Utils from '../../utils';
import defaultConfig from '../../config';
// 导入公共事件处理函数
import { handleFn } from './common.js';

// 组件名称定义
defineOptions({
	name: 'CoSelect',
	inheritAttrs: false, // 不继承父组件的属性，避免属性传递冲突
});

// 组件属性定义
const props = defineProps({
	// 插槽名称，用于前置/后置插槽场景
	slotName: {
		type: String,
		default: '',
	},
	// 字典数据对象
	dic: {
		type: Object,
		default: () => null,
	},
});

// 获取父组件传递的属性
const attrs = useAttrs();

// 注入父级提供的 widgetItem 对象，用于组件间通信
const widgetItem = inject('widgetItem');

// 响应式数据定义
const listeners = reactive({}); // 事件监听器对象
const labelKey = ref(defaultConfig.dicKeys[0]); // 显示文本的键名
const valueKey = ref(defaultConfig.dicKeys[1]); // 选项值的键名
const optionList = ref([]); // 选项数据列表

// 初始化组件数据（原created()逻辑）
const { data = null, item, row = data, scene, mainProp = '' } = attrs;
const _inTable = scene === 'inTable'; // 是否在表格内使用
const formModel = reactive(row); // 表单数据模型（响应式）
const itemAttrs = reactive(Object.assign(item.attrs || {})); // 表单项属性配置

// 处理前置插槽的属性名
if (mainProp) {
	item.prop = `${mainProp}_prepend`;
}

// 初始化字段默认值
if (!formModel[item.prop]) {
	formModel[item.prop] = itemAttrs.multiple ? [] : '';
}

// 处理多选模式的数据格式转换
if (itemAttrs.multiple) {
	const itemProp = row[item.prop];
	if (['String', 'Number'].includes(Utils.getType(itemProp))) {
		formModel[item.prop] = itemProp.toString().split(',').map(Number);
	}
}

// 将当前组件实例注册到 widgetItem 中，用于父组件访问
widgetItem[item.prop] = {
	// 重置字段方法
	resetField: (value = undefined) => {
		formModel[item.prop] = value;
	}
};

// 获取选项配置
const { optionKey, option, attrs: itemAttrsConfig, filter } = item;
const optionVal = option || itemAttrsConfig.option;

// 选项过滤函数
const filterOption = (options) => (typeof filter === 'function' ? filter(options) : options);

// 设置键名映射
if (optionKey) {
	[labelKey.value, valueKey.value] = optionKey;
}

/**
 * 初始化选项数据
 * 根据不同的选项配置类型加载选项数据
 *
 * @param {Object} item - 表单项配置
 * @param {Array} optionKey - 键名映射数组
 * @param {*} optionVal - 选项配置值
 */
const initOption = (item, optionKey, optionVal) => {
	// 获取选项值的数据类型
	const optionValueType = Utils.getType(optionVal);
	
	// 如果选项值是数组，直接使用
	if (optionValueType === 'Array') {
		optionList.value = filterOption(optionVal);
	}
	// 如果选项值是字符串且以.json结尾，从字典中获取数据
	else if (optionValueType === 'String' && optionVal.substr(optionVal.length - 5, 5) === '.json') {
		const pval = optionVal.replace(/\.json$/, '');
		defaultConfig.getDic(pval).then((resDic) => {
			optionList.value = filterOption(resDic);
		});
	}
};

/**
 * 初始化事件监听器
 * 设置表单组件的事件处理逻辑
 */
const initEvent = () => {
	// 绑定change事件
	listeners['change'] = (value) => handleFn.call({ formModel, item, _inTable }, 'change', value, _inTable, attrs, itemAttrs);
	
	// 绑定自定义事件
	const { events } = item;
	if (events) {
		for (const evName of Object.keys(events)) {
			if (_inTable) {
				listeners[evName] = () => handleFn.call({ formModel, item, _inTable }, evName, attrs.row[item.prop], _inTable, attrs, itemAttrs);
			} else {
				listeners[evName] = () => handleFn.call({ formModel, item, _inTable }, evName, attrs.data[attrs.item.prop], false, attrs, itemAttrs);
			}
		}
	}
};

// 监听字典数据变化
watch(() => props.dic, (val) => {
	if (val && val[optionVal]) {
		initOption(item, optionKey, val[optionVal]);
	}
}, { deep: true });

// 初始化选项数据和事件
initOption(item, optionKey, optionVal);
initEvent();
</script>

<style>
:root {
	--el-input-border-radius: 4px;
}
.el-input-group--prepend .el-input__wrapper {
	border-top-left-radius: 0;
	border-bottom-left-radius: 0;
}
</style>
