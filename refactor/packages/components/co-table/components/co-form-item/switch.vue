<!--
  Co-Switch 开关组件

  功能特性：
  - 基于 Element Plus Switch 组件封装
  - 支持异步状态变更验证
  - 支持加载状态显示
  - 自动阻止事件冒泡
  - 响应式数据绑定

  使用场景：
  - 表格内状态切换
  - 表单开关控制
  - 需要异步验证的开关操作
-->
<template>
	<div @click.stop>
		<!--
			Element Plus 开关组件
			- v-model: 双向绑定开关状态
			- v-bind="item.attrs": 绑定所有配置属性
			- :loading: 显示加载状态
			- :before-change: 状态变更前的验证处理
			- @change: 监听状态变更事件
		-->
		<el-switch 
			v-model="formModel[item.prop]" 
			v-bind="item.attrs" 
			:loading="loading" 
			:before-change="onBeforeChange" 
			@change="onChange" 
		/>
	</div>
</template>

<script setup>
/**
 * Co-Switch 开关组件
 *
 * 基于 Element Plus Switch 组件封装的开关组件
 * 支持表格内编辑和搜索表单两种使用场景
 *
 * 主要功能：
 * - 响应式数据绑定
 * - 异步状态变更验证
 * - 加载状态管理
 * - 事件统一处理
 * - 字段重置功能
 *
 * <AUTHOR> 4.0 sonnet
 * @version 1.0.0
 */

// 导入Vue 3 Composition API
import { ref, inject, useAttrs } from 'vue';
// 导入公共事件处理函数
import { handleFn } from './common.js';

// 组件名称定义
defineOptions({
	name: 'CoSwitch',
	inheritAttrs: false, // 不继承父组件的属性，避免属性传递冲突
});

// 获取父组件传递的属性
const attrs = useAttrs();

// 注入父级提供的 widgetItem 对象，用于组件间通信
const widgetItem = inject('widgetItem');

// 响应式数据定义
const loading = ref(false); // 加载状态

// 初始化组件数据（原created()逻辑）
const { item, data = null, row, scene = '' } = attrs;
const formModel = data || row; // 表单数据模型
const _inTable = scene === 'inTable'; // 是否在表格内使用

// 将当前组件实例注册到 widgetItem 中，用于父组件访问
widgetItem[item.prop] = {
	// 重置字段方法
	resetField: (value = undefined) => {
		formModel[item.prop] = value;
	}
};

/**
 * 状态变更前的验证处理
 * 支持异步验证逻辑，在状态变更前执行自定义验证
 *
 * @returns {boolean|Promise} 验证结果，true表示允许变更，false表示阻止变更
 */
const onBeforeChange = () => {
	// 检查是否配置了自定义验证函数
	const hasBeforeChange = item.attrs && item.attrs['before-change'];
	
	// 如果没有配置验证函数，直接允许变更
	if (!hasBeforeChange) return true;
	
	// 显示加载状态
	loading.value = true;
	
	// 执行异步验证
	const result = item.attrs['before-change'](formModel).then((res) => {
		// 验证完成后隐藏加载状态
		loading.value = false;
		return res;
	});
	
	// 触发开关事件处理
	handleFn.call({ formModel, item, _inTable }, 'switch', formModel[item.prop], _inTable, attrs, item.attrs);
	
	return result;
};

/**
 * 状态变更事件处理
 * 当开关状态发生变化时触发
 *
 * @param {boolean} value - 新的开关状态值
 */
const onChange = (value) => {
	// 如果没有配置异步验证，直接处理变更事件
	if (!item.attrs || !item.attrs['before-change']) {
		handleFn.call({ formModel, item, _inTable }, 'switch', value, _inTable, attrs, item.attrs);
	}
};
</script>
