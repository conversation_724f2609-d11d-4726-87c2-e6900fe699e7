/**
 * Co-Form-Item 表单组件集合导出文件
 *
 * 该文件负责自动导入当前目录下的所有 Vue 组件文件，
 * 并以组件名称为键名导出一个组件对象集合。
 *
 * 包含的组件：
 * - CoInput: 输入框组件
 * - CoSelect: 下拉选择器组件
 * - CoDate: 日期时间组件
 * - CoSwitch: 开关组件
 *
 * 使用方式：
 * import coFormItem from './co-form-item/index.js';
 *
 * 然后在 Vue 组件中注册：
 * components: {
 *   ...coFormItem
 * }
 *
 * <AUTHOR> 4.0 sonnet
 * @version 1.0.0
 */

// 注释掉的旧代码：使用 webpack 的 require.context API
// const requireComponent = require.context('./', false, /\w+\.vue$/)

/**
 * 使用 Vite 的 import.meta.glob API 动态导入所有 Vue 组件
 *
 * 参数说明：
 * - './*.vue': 匹配当前目录下所有 .vue 文件
 * - { eager: true }: 立即加载所有匹配的模块，而不是懒加载
 *
 * 返回格式：
 * {
 *   './input.vue': { default: InputComponent },
 *   './select.vue': { default: SelectComponent },
 *   ...
 * }
 */
const requireComponent = import.meta.glob('./*.vue', { eager: true });

// 创建组件集合对象
const comps = {};

/**
 * 遍历所有导入的组件文件，提取组件并以组件名为键名存储
 */
Object.keys(requireComponent).forEach((fileName) => {
	// 获取组件的默认导出（Vue 组件对象）
	const comp = requireComponent[fileName].default;

	// 以组件的 name 属性作为键名，组件对象作为值
	// 例如：{ 'CoInput': InputComponent, 'CoSelect': SelectComponent, ... }
	comps[comp.name] = comp;
});

/**
 * 导出组件集合对象
 *
 * 导出格式：
 * {
 *   CoInput: InputComponent,
 *   CoSelect: SelectComponent,
 *   CoDate: DateComponent,
 *   CoSwitch: SwitchComponent
 * }
 */
export default comps;
