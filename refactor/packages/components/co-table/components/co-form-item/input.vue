<!--
  Co-Input 输入框组件

  功能特性：
  - 支持前置和后置插槽内容
  - 自动生成占位符文本
  - 支持前置/后置下拉选择器
  - 自动去除首尾空格
  - 响应式数据绑定

  使用场景：
  - 表格内可编辑单元格
  - 搜索表单输入项
  - 带前缀/后缀的复合输入框
-->
<template>
	<!--
		Element Plus 输入框组件
		- v-model.trim: 双向绑定并自动去除首尾空格
		- v-bind="itemAttrs": 绑定所有配置属性
		- placeholder: 动态生成占位符文本
		- v-on="listeners": 绑定所有事件监听器
	-->
	<el-input
		v-model.trim="formModel[item.prop]"
		v-bind="itemAttrs"
		:placeholder="itemAttrs.placeholder || '请输入' + (itemAttrs.label || '')"
		v-on="listeners"
	>
		<!-- 动态插槽：支持前置(prepend)和后置(append)内容 -->
		<template v-for="slot in slotList" #[slot.name]>
			<!-- 如果插槽内容是字符串，直接显示文本 -->
			<template v-if="typeof item[slot.name] === 'string'">
				{{ item[slot.name] }}
			</template>
			<!-- 如果插槽内容是配置对象，渲染为下拉选择器 -->
			<co-select
				v-else
				:key="slot.name"
				v-bind="{
					item: slot[slot.name],
					dic: $attrs.dic,
					mainProp: slot[slot.name].prop ? '' : item.prop
				}"
				:row="formModel"
				:style="{ minWidth: slot[slot.name].width || '80px' }"
				@change="onPendChange"
			/>
		</template>
	</el-input>
</template>

<script setup>
/**
 * Co-Input 输入框组件
 *
 * 基于 Element Plus Input 组件封装的表单输入框
 * 支持表格内编辑和搜索表单两种使用场景
 *
 * 主要功能：
 * - 响应式数据绑定
 * - 前置/后置插槽支持
 * - 事件统一处理
 * - 字段重置功能
 * - 插槽内容动态渲染
 *
 * <AUTHOR> 4.0 sonnet
 * @version 1.0.0
 */

// 导入Vue 3 Composition API
import { reactive, inject, useAttrs, computed } from 'vue';
// 导入公共事件处理函数
import { handleFn } from './common.js';
// 导入下拉选择器组件（用于前置/后置插槽）
import coSelect from './select.vue';

// 组件名称定义
defineOptions({
	name: 'CoInput',
	inheritAttrs: false, // 不继承父组件的属性，避免属性传递冲突
});

// 注册子组件
defineOptions({
	components: {
		coSelect,
	},
});

// 获取父组件传递的属性
const attrs = useAttrs();

// 注入父级提供的 widgetItem 对象，用于组件间通信
const widgetItem = inject('widgetItem');

// 响应式数据定义
const listeners = reactive({}); // 事件监听器对象

// 初始化组件数据（原created()逻辑）
const { item, data = null, row = data, scene } = attrs;
const _inTable = scene === 'inTable'; // 是否在表格内使用
const formModel = reactive(row); // 表单数据模型（响应式）
const itemAttrs = reactive(Object.assign(item.attrs || {})); // 表单项属性配置

// 初始化字段默认值
if (!formModel[item.prop]) {
	formModel[item.prop] = '';
}

// 将当前组件实例注册到 widgetItem 中，用于父组件访问
widgetItem[item.prop] = {
	// 重置字段方法
	resetField: (value = '') => {
		formModel[item.prop] = value;
	}
};

// 计算插槽列表（原computed逻辑）
const slotList = computed(() => {
	const slots = [];
	
	// 检查前置插槽
	if (item.prepend) {
		slots.push({ name: 'prepend' });
	}
	
	// 检查后置插槽
	if (item.append) {
		slots.push({ name: 'append' });
	}
	
	return slots;
});

/**
 * 初始化事件监听器
 * 设置表单组件的事件处理逻辑
 */
const initEvent = () => {
	// 绑定input事件（实时输入）
	listeners['input'] = (value) => handleFn.call({ formModel, item, _inTable }, 'input', value, _inTable, attrs, itemAttrs);
	
	// 绑定change事件（失去焦点或回车）
	listeners['change'] = (value) => handleFn.call({ formModel, item, _inTable }, 'change', value, _inTable, attrs, itemAttrs);
	
	// 绑定自定义事件
	const { events } = item;
	if (events) {
		for (const evName of Object.keys(events)) {
			if (_inTable) {
				// 表格内事件处理
				listeners[evName] = () => handleFn.call({ formModel, item, _inTable }, evName, attrs.row[item.prop], _inTable, attrs, itemAttrs);
			} else {
				// 搜索表单事件处理
				listeners[evName] = () => handleFn.call({ formModel, item, _inTable }, evName, attrs.data[attrs.item.prop], false, attrs, itemAttrs);
			}
		}
	}
};

/**
 * 前置/后置插槽变化事件处理
 * 当前置或后置下拉选择器的值发生变化时触发
 *
 * @param {Object} data - 变化的数据对象，包含prop和value
 */
const onPendChange = (data) => {
	// 触发统一事件处理
	handleFn.call({ formModel, item, _inTable }, 'change', data, _inTable, attrs, itemAttrs);
};

// 初始化事件监听器
initEvent();
</script>
