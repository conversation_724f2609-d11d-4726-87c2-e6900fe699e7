<!--
  Co-Input 输入框组件

  功能特性：
  - 支持前置和后置插槽内容
  - 自动生成占位符文本
  - 支持前置/后置下拉选择器
  - 自动去除首尾空格
  - 响应式数据绑定

  使用场景：
  - 表格内可编辑单元格
  - 搜索表单输入项
  - 带前缀/后缀的复合输入框
-->
<template>
	<!--
		Element Plus 输入框组件
		- v-model.trim: 双向绑定并自动去除首尾空格
		- v-bind="itemAttrs": 绑定所有配置属性
		- placeholder: 动态生成占位符文本
		- v-on="listeners": 绑定所有事件监听器
	-->
	<el-input
		v-model.trim="formModel[item.prop]"
		v-bind="itemAttrs"
		:placeholder="itemAttrs.placeholder || '请输入' + (itemAttrs.label || '')"
		v-on="listeners"
	>
		<!-- 动态插槽：支持前置(prepend)和后置(append)内容 -->
		<template v-for="slot in slotList" #[slot.name]>
			<!-- 如果插槽内容是字符串，直接显示文本 -->
			<template v-if="typeof item[slot.name] === 'string'">
				{{ item[slot.name] }}
			</template>
			<!-- 如果插槽内容是配置对象，渲染为下拉选择器 -->
			<co-select
				v-else
				:key="slot.name"
				v-bind="{
					item: slot[slot.name],
					dic: $attrs.dic,
					mainProp: slot[slot.name].prop ? '' : item.prop
				}"
				:row="formModel"
				:style="{ minWidth: slot[slot.name].width || '80px' }"
				@change="onPendChange"
			/>
		</template>
	</el-input>
</template>
<script setup>
/**
 * Co-Input 输入框组件
 *
 * 基于 Element Plus Input 组件封装的表单输入框
 * 支持表格内编辑和搜索表单两种使用场景
 *
 * 主要功能：
 * - 响应式数据绑定
 * - 前置/后置插槽支持
 * - 事件统一处理
 * - 字段重置功能
 * - 插槽内容动态渲染
 *
 * <AUTHOR> 4.0 sonnet
 * @version 1.0.0
 */

// 导入Vue 3 Composition API
import { reactive, inject, useAttrs } from 'vue';
// 导入公共事件处理函数
import { handleFn } from './common.js';
// 导入下拉选择器组件（用于前置/后置插槽）
import coSelect from './select.vue';

// 组件名称定义
defineOptions({
	name: 'CoInput',
	inheritAttrs: false, // 不继承父组件的属性，避免属性传递冲突
});

// 定义事件
const emit = defineEmits(['change']);

// 获取父组件传递的属性
const attrs = useAttrs();

// 注入父级提供的 widgetItem 对象，用于组件间通信
const widgetItem = inject('widgetItem');

// 初始化逻辑（原created()逻辑）
// 解构获取需要的属性和数据
const { item, data = null, row = data, scene } = attrs;

// 合并表单项属性，默认启用清除功能
const itemAttrs = Object.assign(item.attrs || {}, { clearable: true });

// 判断是否在表格内使用
const _inTable = scene === 'inTable';

// 创建响应式的表单数据模型
const formModel = reactive(row);

// 初始化字段值（如果字段不存在，设置为空字符串）
if (!formModel[item.prop]) {
	formModel[item.prop] = '';
}

// 将当前组件实例注册到 widgetItem 中，供其他组件访问
// 注意：在Composition API中，我们需要暴露resetField方法
const resetField = (value) => {
	formModel[item.prop] = value;
};

// 注册到widgetItem
widgetItem[item.prop] = { resetField };

// 初始化插槽列表
const slotList = [];

// 检查并配置前置插槽
if (item.prepend) {
	slotList.push({
		name: 'prepend',        // 插槽名称
		prepend: item.prepend,  // 插槽配置
	});
}

// 检查并配置后置插槽
if (item.append) {
	slotList.push({
		name: 'append',         // 插槽名称
		append: item.append,    // 插槽配置
	});
}

// 事件监听器对象，存储所有绑定的事件处理函数
const listeners = {};

// 绑定默认的 change 事件处理器
listeners['change'] = (value) => handleFn.call({ $emit: emit, cacheKey: undefined, resetField }, 'change', value, _inTable, attrs, itemAttrs);

// 如果在表格内使用且配置了自定义事件，绑定额外的事件处理器
if (_inTable && item.events) {
	for (const evName of Object.keys(item.events)) {
		// 为每个自定义事件创建处理器
		listeners[evName] = () => handleFn.call({ $emit: emit, cacheKey: undefined, resetField }, evName, row[item.prop], _inTable, attrs, itemAttrs);
	}
}

/**
 * 处理前置/后置组件的值变化
 * 当前置或后置的下拉选择器值发生变化时触发
 *
 * @param {Object} data - 变化的数据对象
 * @param {string} data.prop - 属性名
 * @param {*} data.value - 新值
 */
const onPendChange = (data) => {
	// 向父组件发送 change 事件
	emit('change', { prop: data.prop, value: data.value });
};
</script>
