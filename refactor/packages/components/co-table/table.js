/**
 * Co-Table 组件表格逻辑处理工具对象
 *
 * 包含表格组件的核心业务逻辑方法，主要功能：
 * - 行选择状态管理
 * - 属性动态设置
 * - 事件监听处理
 * - 参数过滤
 * - 存储数据匹配
 *
 * <AUTHOR> 4.0 sonnet
 * @version 1.0.0
 */
const tableJs = {
	/**
	 * 切换行的选中状态
	 * 支持单选模式和多选模式，支持树形结构的级联选择
	 *
	 * @param {Object} row - 要切换选中状态的行数据对象
	 * @param {boolean} [selected=true] - 目标选中状态，true为选中，false为取消选中
	 * @param {Function} elToggleRowSelection - Element Plus表格的原生切换选中方法
	 *
	 * @example
	 * // 选中某一行
	 * tableJs.toggleRowSelection.call(this, rowData, true, this.elTableRef.toggleRowSelection);
	 *
	 * // 取消选中某一行
	 * tableJs.toggleRowSelection.call(this, rowData, false, this.elTableRef.toggleRowSelection);
	 */
	toggleRowSelection: function (row, selected = true, elToggleRowSelection) {
		// 获取当前行的唯一标识字段名（如'id'、'_uuid'等）
		const cRowKey = this.currentRowKey;
		// 获取子节点的字段名（用于树形结构，如'children'）
		const childKey = this._childrenKey;

		// 判断是否为单选模式
		if (this.singleMode) {
			// 单选模式：直接设置单选数据和选中数据
			this.singleData = row[cRowKey];
			this.selectedData = [row];
		} else {
			// 多选模式：需要处理复杂的选中逻辑

			/**
			 * 递归设置行及其子行的选中状态
			 * @param {Object} row - 当前处理的行数据
			 * @param {boolean} selected - 目标选中状态
			 */
			const setToggleRow = (row, selected) => {
				// 设置行的内部选中标记
				row._selected = selected;

				if (selected) {
					// 如果是选中操作，且当前行没有子节点，则添加到选中数据数组
					!row[childKey] && this.selectedData.push(row);
				} else {
					// 如果是取消选中操作，从选中数据数组中移除该行
					this.selectedData.splice(
						this.selectedData.findIndex((item) => item[cRowKey] === row[cRowKey]),
						1
					);
				}

				// 如果当前行有子节点，递归处理所有子节点
				if (row[childKey]) {
					for (let child of row[childKey]) {
						setToggleRow(child, selected);
					}
				}
			};

			// 执行递归选中状态设置
			setToggleRow(row, selected);
		}

		// 调用Element Plus表格的原生方法更新UI显示
		elToggleRowSelection(row, selected);
	},
	/**
	 * 动态设置目标对象的属性
	 * 用于动态修改表头配置或行数据的属性值
	 *
	 * @param {Object} target - 要修改的目标对象（如表头配置对象或行数据对象）
	 * @param {Object} data - 包含新属性值的数据对象
	 *
	 * @example
	 * // 修改表头配置
	 * tableJs.setProp.call(this, headerItem, { width: 200, label: '新标题' });
	 *
	 * // 修改行数据
	 * tableJs.setProp.call(this, rowData, { status: 1, name: '新名称' });
	 */
	setProp(target, data) {
		// 将数据对象转换为响应式的键值对数组
		const entriesData = reactive(Object.entries(data));

		// 遍历所有键值对，将新值赋给目标对象
		for (const [key, value] of entriesData) {
			// 无论目标对象是否已有该属性，都直接赋值
			// 注意：这里的三元运算符实际上两个分支都是赋值操作，可以简化
			target[key] ? (target[key] = value) : (target[key] = value);
		}
	},

	/**
	 * 获取行在表格中的索引位置
	 * 支持普通表格和树形表格的索引获取
	 *
	 * @param {Object} row - 要获取索引的行数据对象
	 * @returns {number|Array} 普通行返回数字索引，树形结构返回索引数组
	 *
	 * @example
	 * // 普通表格行
	 * const index = tableJs.getRowIndex.call(this, rowData); // 返回 2
	 *
	 * // 树形表格行
	 * const index = tableJs.getRowIndex.call(this, childRowData); // 返回 [1, 0]
	 */
	getRowIndex(row) {
		// 获取当前行的唯一标识字段名
		const crowKey = this.currentRowKey;

		// 判断是否为树形结构的行（有子节点）
		return row[this._childrenKey] && row[this._childrenKey].length
			? row._index  // 树形结构返回预设的索引数组
			: this.tableData.findIndex((item) => item[crowKey] === row[crowKey]); // 普通结构返回在数组中的位置
	},
	/**
	 * 初始化表格事件监听器
	 * 为Element Plus表格添加自定义的事件处理逻辑
	 *
	 * @param {Object} listeners - 现有的事件监听器对象
	 * @returns {Object} 合并后的事件监听器对象
	 *
	 * @description
	 * 该方法会添加以下事件监听器：
	 * - select-all: 全选/取消全选事件
	 * - select: 单行选择事件
	 * - row-click: 行点击事件
	 * - selection-change: 选择变化事件
	 */
	initListeners: function (listeners) {
		// 只有当存在监听器时才进行处理
		if (Object.keys(listeners).length) {
			// 获取表格的唯一标识
			const tableKey = this._tableKey;

			// 将自定义事件处理器合并到现有监听器中
			Object.assign(listeners, {
				/**
				 * 全选/取消全选事件处理器
				 * @param {Array} selection - 当前选中的行数据数组
				 */
				'select-all': (selection) => {
					// 遍历所有表格数据，更新每行的选中状态
					this.tableData.forEach((row) => {
						// 如果有选中项，则切换当前行的选中状态；否则设为未选中
						row._selected = !!selection.length ? !row._selected : false;
					});

					// 更新组件的选中数据
					this.selectedData = selection;

					// 向父组件发送全选事件，包含选中数据和表格ID
					this.$emit('select-all', { selection, tableId: tableKey });
				},

				/**
				 * 单行选择事件处理器
				 * @param {Array} selection - 当前选中的行数据数组
				 * @param {Object} row - 被选择的行数据
				 */
				select: (selection, row) => {
					// 切换当前行的选中状态
					row._selected = !row._selected;

					// 更新组件的选中数据（用于修改选中背景色）
					this.selectedData = selection;

					// 向父组件发送选择事件，包含选中数据、行数据、索引和表格ID
					this.$emit('select', {
						selection,
						row,
						index: tableJs.getRowIndex.call(this, row),
						tableId: tableKey
					});
				},

				/**
				 * 行点击事件处理器
				 * @param {Object} row - 被点击的行数据
				 * @param {Object} column - 被点击的列配置
				 * @param {Event} event - 原始点击事件对象
				 */
				'row-click': (row, column, event) => {
					// 如果点击的是选择框列，则不处理
					if (column.type === 'selection') return;

					// 阻止事件冒泡
					event.stopPropagation();

					// 切换行的选中状态
					row._selected = !row._selected;

					// 获取行索引
					const index = tableJs.getRowIndex.call(this, row);

					// 如果启用了行点击选择功能
					if (this.currentRow) {
						// 根据单选模式决定处理方式
						this.singleMode
							? this.singleClick('', row, index, true)  // 单选模式处理
							: this.elTableRef.toggleRowSelection(row, row._selected); // 多选模式处理
					}

					// 向父组件发送行点击事件
					this.$emit('row-click', { row, column, event, index, tableId: tableKey });
				},

				/**
				 * 选择变化事件处理器
				 * @param {Array} selection - 当前选中的行数据数组
				 */
				'selection-change': (selection) => {
					// 更新组件的选中数据
					this.selectedData = selection;

					// 向父组件发送选择变化事件
					this.$emit('selection-change', selection);
				},
			});
		}

		// 返回合并后的监听器对象
		return listeners;
	},
	/**
	 * 过滤查询参数中的空值
	 * 根据不同的配置策略，移除或保留空值参数
	 *
	 * @param {Object} queryData - 要过滤的查询参数对象（会被直接修改）
	 * @param {boolean|Array} safeNullParams - 空值处理配置
	 * @param {string} paramsType - 参数类型：'Boolean' 或 'Array'
	 *
	 * @description
	 * - 当 paramsType 为 'Boolean' 且 safeNullParams 为 false 时，移除所有空值
	 * - 当 paramsType 为 'Array' 时，只移除不在 safeNullParams 数组中的空值字段
	 *
	 * @example
	 * const params = { name: '', age: 18, city: null, id: undefined };
	 *
	 * // 移除所有空值
	 * tableJs.filterNullParams(params, false, 'Boolean');
	 * // params 变为 { age: 18 }
	 *
	 * // 保护指定字段
	 * tableJs.filterNullParams(params, ['name'], 'Array');
	 * // params 变为 { name: '', age: 18 }
	 */
	filterNullParams: function (queryData, safeNullParams, paramsType) {
		// 布尔模式：当 safeNullParams 为 false 时，移除所有空值
		if (paramsType === 'Boolean' && !safeNullParams) {
			// 遍历所有参数，删除空值字段
			for (let [key, value] of Object.entries(queryData)) {
				// 如果值为空字符串、undefined 或 null，则删除该字段
				(value === '' || value === undefined || value === null) && delete queryData[key];
			}
			return;
		}

		// 数组模式：只删除不在保护列表中的空值字段
		if (paramsType === 'Array') {
			// 获取所有参数键名
			const paramsArr = Object.keys(queryData);
			// 过滤出不在保护列表中的参数键名
			const removeParams = paramsArr.filter((item) => !safeNullParams.includes(item));

			// 遍历需要检查的参数，删除其中的空值
			for (let key of removeParams) {
				(queryData[key] === undefined || queryData[key] === null || queryData[key] === '') && delete queryData[key];
			}
		}
	},

	/**
	 * 根据属性名获取组件属性的值或执行结果
	 * 支持静态属性值和动态函数属性
	 *
	 * @param {string} propsName - 要获取的属性名称
	 * @param {*} data - 传递给函数属性的参数数据
	 * @returns {*} 属性值或函数执行结果，不存在时返回空字符串
	 *
	 * @example
	 * // 获取静态属性
	 * const className = tableJs.getResultByPros.call(this, 'row-class-name');
	 *
	 * // 获取函数属性的执行结果
	 * const dynamicClass = tableJs.getResultByPros.call(this, 'row-class-name', rowData);
	 */
	getResultByPros: function (propsName, data) {
		// 从组件的 $attrs 中获取指定属性
		const attrsProp = this.$attrs[propsName];

		// 如果属性存在，判断是函数还是静态值
		return attrsProp
			? (typeof attrsProp === 'function' ? attrsProp(data) : attrsProp)  // 函数则执行，否则直接返回
			: '';  // 属性不存在返回空字符串
	},

	/**
	 * 匹配并加载存储数据到全局 storage 对象
	 * 用于按钮权限规则中访问 localStorage 或 sessionStorage 的数据
	 *
	 * @param {string} rule - 包含存储访问路径的规则字符串
	 *
	 * @description
	 * 该方法会解析规则字符串中的 storage.xxx 模式，
	 * 从 sessionStorage 或 localStorage 中加载对应数据到 window.storage
	 *
	 * @example
	 * // 规则字符串：'storage.userInfo.role === "admin"'
	 * // 会从存储中加载 userInfo 数据到 window.storage.userInfo
	 * tableJs.matchStorage('storage.userInfo.role === "admin"');
	 */
	matchStorage: (rule) => {
		// 使用正则表达式匹配 storage.xxx 模式，提取存储键路径
		const matchRes = rule.match(/storage.(\S*)(?=)/);

		// 将匹配到的路径按点号分割成数组
		const varKeys = matchRes[1].split('.');

		// 获取根键名（第一级键名）
		const varKeys0 = varKeys[0];

		// 如果全局 storage 中已存在该键，则不需要重复加载
		if (window.storage[varKeys0]) return;

		// 尝试从 sessionStorage 或 localStorage 中获取数据
		const rootKey = sessionStorage[varKeys0] || localStorage[varKeys0];

		if (rootKey) {
			// 如果存储的数据存在，根据路径层级决定是否需要 JSON 解析
			window.storage[varKeys0] = varKeys.length > 1
				? JSON.parse(rootKey)  // 多层级路径，需要解析 JSON
				: rootKey;             // 单层级路径，直接使用原值
		}
	},
};

// 导出表格逻辑工具对象
export default tableJs;
