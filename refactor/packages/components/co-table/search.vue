<!--
  Co-Search 搜索组件

  功能特性：
  - 基于 Element Plus Form 组件封装的动态搜索表单
  - 支持多种表单组件类型和复杂的数据处理逻辑
  - 动态表单项渲染
  - 前置组件处理
  - 分割字段支持
  - 搜索和重置功能
  - 数据回显和设置

  使用场景：
  - 表格顶部搜索表单
  - 独立的搜索表单
  - 需要动态配置的查询界面
-->
<template>
	<!--
		Element Plus 表单组件
		- ref="searchFormRef": 表单引用，用于访问表单方法
		- :model="model": 绑定表单数据模型
		- v-bind="$attrs": 绑定父组件传递的所有属性
		- :inline: 内联模式，默认为 true
		- :size: 表单组件尺寸，默认为 'default'
		- :validate-on-rule-change="false": 禁用规则变化时的自动验证
		- :[styleName]="_styles": 动态绑定样式属性（class 或 style）
	-->
	<el-form
		ref="searchFormRef"
		:model="model"
		v-bind="$attrs"
		:inline="$attrs.inline || true"
		:size="$attrs.size || 'default'"
		:validate-on-rule-change="false"
		:[styleName]="_styles"
	>
		<!-- 动态渲染搜索表单项 -->
		<template v-for="item in config.items" :key="item.prop">
			<!--
				表单项容器
				- v-if="!item.hidden": 只渲染非隐藏的表单项
				- :prop="item.prop": 设置表单项的属性名
				- v-bind="item.attrs": 绑定表单项的配置属性
			-->
			<el-form-item
				v-if="!item.hidden"
				:prop="item.prop"
				v-bind="item.attrs"
			>
				<!--
					动态表单组件
					- :is: 根据类型动态选择组件
					- v-bind: 绑定组件所需的所有属性
					- @change: 监听组件值变化事件
				-->
				<component
					:is="getComponentName(item.type)"
					v-bind="getComponentProps(item)"
					@change="onComponentChange"
				/>
			</el-form-item>
		</template>

		<!-- 操作按钮区域 -->
		<el-form-item v-if="config.items">
			<!--
				操作按钮插槽
				- :name: 根据使用场景动态生成插槽名称
				- :handle: 传递操作处理函数
			-->
			<slot
				:name="$attrs.scene ? 'search_operation' : 'operation'"
				:handle="onHandle"
			>
				<!-- 搜索按钮 -->
				<el-button
					v-bind="searchProps"
					@click="onHandle('search')"
				>
					{{ searchProps.name }}
				</el-button>

				<!-- 重置按钮 -->
				<el-button
					v-bind="resetProps"
					@click="onHandle('reset')"
				>
					{{ resetProps.name }}
				</el-button>
			</slot>
		</el-form-item>
	</el-form>
</template>

<script setup>
/**
 * Co-Search 搜索组件
 *
 * 基于 Element Plus Form 组件封装的动态搜索表单
 * 支持多种表单组件类型和复杂的数据处理逻辑
 *
 * 主要功能：
 * - 动态表单项渲染
 * - 多种表单组件支持
 * - 前置组件处理
 * - 分割字段支持
 * - 搜索和重置功能
 * - 数据回显和设置
 *
 * <AUTHOR> 4.0 sonnet
 * @version 1.0.0
 */

// 导入Vue 3 Composition API
import { ref, reactive, computed, provide, useAttrs } from 'vue';
// 导入默认配置和日期类型枚举
import defaultConfig, { dateType } from './config.js';
// 导入表单组件集合
import coFormItem from './components/co-form-item/index.js';
// 导入工具类
import Utils from './utils';

// 组件名称定义
defineOptions({
	name: 'CoSearch',
	inheritAttrs: false, // 不继承父组件的属性
	components: {
		...coFormItem, // 注册所有表单组件
	},
});

/**
 * 组件属性定义
 */
const props = defineProps({
	// 表单数据模型
	model: {
		type: Object,
		default: () => ({}),
	},
	// 搜索表单配置对象
	config: {
		type: Object,
		default: () => {},
	},
	// 字典数据对象
	dic: {
		type: Object,
		default: () => null,
	},
});

// 定义事件
const emit = defineEmits(['search', 'change']);

// 获取父组件传递的属性
const attrs = useAttrs();

// 表单引用
const searchFormRef = ref(null);

// 响应式数据定义
const formModel = reactive({}); // 表单数据模型（暂未使用）
const widgetItem = reactive({}); // 组件实例集合，用于访问各个表单组件的方法
const oldModel = ref(null); // 初始数据备份，用于重置操作

// 向子组件提供数据
// 使子组件能够访问 widgetItem 对象
provide('widgetItem', widgetItem);

// 计算属性：样式属性名
const styleName = computed(() => {
	const configStyle = props.config.style;
	return configStyle && typeof configStyle === 'object' ? 'style' : 'class';
});

// 计算属性：样式值
const _styles = computed(() => {
	return props.config.style || null;
});

// 计算属性：搜索按钮配置
const searchProps = computed(() => {
	return Object.assign({}, defaultConfig.search.search, props.config.search || {});
});

// 计算属性：重置按钮配置
const resetProps = computed(() => {
	return Object.assign({}, defaultConfig.search.reset, props.config.reset || {});
});

/**
 * 根据类型获取组件名称
 * 将表单项类型映射为对应的组件名称
 *
 * @param {string} type - 表单项类型
 * @returns {string} 组件名称
 */
const getComponentName = (type) => {
	// 日期时间类型映射
	if (dateType.includes(type)) {
		return 'CoDate';
	}
	
	// 其他类型映射
	const typeMap = {
		'input': 'CoInput',
		'select': 'CoSelect',
		'switch': 'CoSwitch',
		'upload': 'CoUpload',
	};
	
	return typeMap[type] || 'CoInput'; // 默认使用输入框
};

/**
 * 获取组件属性
 * 为动态组件准备所需的属性
 *
 * @param {Object} item - 表单项配置
 * @returns {Object} 组件属性对象
 */
const getComponentProps = (item) => {
	return {
		item,
		data: props.model,
		row: props.model,
		scene: 'search', // 标识为搜索场景
		dic: props.dic,
	};
};

/**
 * 组件值变化事件处理
 * 当表单组件的值发生变化时触发
 *
 * @param {Object} data - 变化的数据对象
 */
const onComponentChange = (data) => {
	emit('change', data);
};

/**
 * 处理搜索和重置操作
 * 这是搜索组件的核心方法，处理复杂的数据转换和前置组件逻辑
 *
 * @param {string} [type='search'] - 操作类型：'search' 或 'reset'
 *
 * @description
 * 该方法处理以下复杂场景：
 * 1. 重置操作：恢复所有字段到初始值
 * 2. 搜索操作：处理前置组件、分割字段等复杂数据转换
 * 3. 前置组件的动态字段名生成
 * 4. 日期时间组件的分割字段处理
 */
const onHandle = (type = 'search') => {
	// 获取表单数据模型的所有字段名
	const formModelArr = Object.keys(props.model);
	// 搜索结果对象，用于存储处理后的搜索参数
	const searchResult = {};

	// 遍历所有表单字段进行处理
	for (const widget of formModelArr) {
		if (type === 'reset') {
			// === 重置操作处理 ===

			// 获取字段的初始值
			const setVal = oldModel.value ? oldModel.value[widget] : '';

			// 如果字段对应的组件实例存在，调用其重置方法
			if (widgetItem[widget]) {
				widgetItem[widget].resetField(setVal);
			}
		} else {
			// === 搜索操作处理 ===

			// 获取当前字段的值
			const fieldValue = props.model[widget];

			// 处理前置组件的特殊逻辑
			if (widget.includes('_prepend')) {
				// 前置组件字段名处理
				const mainField = widget.replace('_prepend', '');
				const mainValue = props.model[mainField];
				
				// 组合前置组件和主字段的值
				if (fieldValue && mainValue) {
					searchResult[mainField] = `${fieldValue}_${mainValue}`;
				} else if (mainValue) {
					searchResult[mainField] = mainValue;
				}
			} else if (!formModelArr.includes(`${widget}_prepend`)) {
				// 普通字段处理（排除已被前置组件处理的字段）
				searchResult[widget] = fieldValue;
			}
		}
	}

	// 发送事件
	if (type === 'reset') {
		emit('search', {}, 'reset');
	} else {
		emit('search', searchResult, 'search');
	}
};

// 初始化数据备份
if (props.model && Object.keys(props.model).length > 0) {
	oldModel.value = Utils.deepClone(props.model);
}

// 向父组件暴露方法
defineExpose({
	searchFormRef,
	onHandle,
	widgetItem,
});
</script>
